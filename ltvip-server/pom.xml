<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ltvip</artifactId>
        <groupId>com.leiting</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ltvip-server</artifactId>
    <packaging>war</packaging>

    <name>Lei Ting :: ${pom.artifactId}</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>2.0.2.RELEASE</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.leiting</groupId>
            <artifactId>boot-framework</artifactId>
            <version>0.3.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.aziado</groupId>
            <artifactId>captcha-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.leiting</groupId>
            <artifactId>guise</artifactId>
            <version>0.3.1</version>
        </dependency>
        <!--配置中心-->
        <dependency>
            <groupId>com.leiting</groupId>
            <artifactId>ltconfig-api</artifactId>
            <version>1.4</version>
        </dependency>
        <!--阿里云RocketMQ-->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
            <version>1.8.4.Final</version>
        </dependency>
        <!--devtools热部署-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <version>2.0.2.RELEASE</version>
            <optional>true</optional>
            <scope>true</scope>
        </dependency>
        <!--excel-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.13</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.12</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jodd</groupId>
            <artifactId>jodd-http</artifactId>
            <version>5.2.0</version>
        </dependency>
        <!-- https://avnrepository.com/artifact/com.google.code.gson/gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-mp -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>4.0.8.B</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-miniapp -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>4.0.8.B</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-common -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-common</artifactId>
            <version>4.0.8.B</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-open -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
            <version>4.0.8.B</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>
        <dependency>
            <groupId>cn.thinkingdata</groupId>
            <artifactId>thinkingdatasdk</artifactId>
            <version>1.8.2</version>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.leiting</groupId>
            <version>2.0</version>
            <artifactId>prophet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.leiting</groupId>
            <artifactId>payweb-api</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.18.1</version>
            <exclusions>
                <exclusion>
                    <groupId>io.projectreactor</groupId>
                    <artifactId>reactor-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.leiting</groupId>
            <artifactId>ltvip-api</artifactId>
            <version>ugc-post-24.12.10-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.18.5</version>
        </dependency>
        <dependency>
            <groupId>com.leiting</groupId>
            <artifactId>tools</artifactId>
            <version>1.5.5</version>
        </dependency>
        <!-- 文件编码检测 -->
        <dependency>
            <groupId>com.googlecode.juniversalchardet</groupId>
            <artifactId>juniversalchardet</artifactId>
            <version>1.0.3</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.rholder/guava-retrying -->
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>

        <!-- Resilience4j dependencies for circuit breaker and rate limiting -->
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-spring-boot2</artifactId>
            <version>1.7.1</version>
        </dependency>
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-circuitbreaker</artifactId>
            <version>1.7.1</version>
        </dependency>
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-ratelimiter</artifactId>
            <version>1.7.1</version>
        </dependency>
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-micrometer</artifactId>
            <version>1.7.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <version>2.0.2.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>2.0.2.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.13.6</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>database.properties</include>
                    <include>application.yml</include>
                    <include>logback-spring.xml</include>
                    <include>config.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>database.properties</exclude>
                    <exclude>application.yml</exclude>
                    <exclude>logback-spring.xml</exclude>
                    <exclude>config.properties</exclude>
                </excludes>
            </resource>
        </resources>
    </build>
    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>profile-dev.properties</filter>
                </filters>
            </build>
            <dependencies>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>member-api</artifactId>
                    <version>0.3</version>
                </dependency>
                <dependency>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                    <version>2.9.2</version>
                </dependency>
                <dependency>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                    <version>2.9.5</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>gameproxy-api</artifactId>
                    <version>0.1</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>iphelper-api</artifactId>
                    <version>0.1</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>db-query</artifactId>
                    <version>3.0</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>prop-api</artifactId>
                    <version>0.2.1</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>loginweb-api</artifactId>
                    <version>1.3</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>manage-api</artifactId>
                    <version>1.4.1</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>test</id>
            <build>
                <filters>
                    <filter>profile-test.properties</filter>
                </filters>
            </build>
            <dependencies>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>member-api</artifactId>
                    <version>0.3</version>
                </dependency>
                <dependency>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                    <version>2.9.2</version>
                </dependency>
                <dependency>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                    <version>2.9.5</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>gameproxy-api</artifactId>
                    <version>0.1-test</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>iphelper-api</artifactId>
                    <version>0.1-test</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>db-query</artifactId>
                    <version>3.0</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>prop-api</artifactId>
                    <version>0.2.1-test</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>loginweb-api</artifactId>
                    <version>test-1.3</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>manage-api</artifactId>
                    <version>1.4.1-test</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>prod</id>
            <build>
                <filters>
                    <filter>profile-prod.properties</filter>
                </filters>
            </build>
            <dependencies>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>ltvip-api</artifactId>
                    <version>ugc-post-24.12.10-RELEASE</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>member-api</artifactId>
                    <version>0.3</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>gameproxy-api</artifactId>
                    <version>0.1</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>iphelper-api</artifactId>
                    <version>0.1</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>db-query</artifactId>
                    <version>3.0</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>prop-api</artifactId>
                    <version>0.2.1</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>loginweb-api</artifactId>
                    <version>1.3</version>
                </dependency>
                <dependency>
                    <groupId>com.leiting</groupId>
                    <artifactId>manage-api</artifactId>
                    <version>1.4.1</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
