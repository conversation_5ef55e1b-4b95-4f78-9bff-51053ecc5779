# 鲸域API Resilience4j 分布式流量整型和熔断使用说明

## 概述

本项目为鲸域API集成了Resilience4j，使用注解+AOP的方式实现了分布式流量整型（平滑请求）和熔断功能，提高系统的稳定性和可靠性。

## 功能特性

### 1. 流量整型（Rate Limiting）
- **智能限流**: 根据JingyuURI.rateLimit字段自动判断是否启用限流
- **发送接口限流**: 每秒最多允许10个请求
- **平滑处理**: 避免瞬时大量请求对下游服务造成冲击
- **超时控制**: 请求等待许可的超时时间可配置

### 2. 熔断器（Circuit Breaker）
- **智能熔断**: 根据JingyuURI.rateLimit字段自动判断是否启用熔断
- **发送接口熔断**: 失败率达到50%时触发熔断
- **慢调用检测**: 自动识别和处理慢调用
- **自动恢复**: 支持半开状态自动测试服务恢复

### 3. 注解+AOP实现
- **声明式**: 使用@JingyuResilience注解标记需要保护的方法
- **自动判断**: AOP切面根据JingyuURI参数自动决定是否应用保护
- **统一处理**: 所有异常和响应处理都在切面中统一管理

### 4. 监控和管理
- **实时监控**: 定时收集限流器和熔断器指标
- **管理接口**: 提供REST API查看状态和管理
- **健康检查**: 集成健康检查端点
- **紧急重置**: 支持手动重置熔断器状态

## 配置说明

### application.yml配置

```yaml
# Resilience4j配置 - 鲸域API流量整型和熔断
resilience4j:
  ratelimiter:
    instances:
      jingyuSend:
        limit-for-period: 10 # 每个周期允许的请求数
        limit-refresh-period: 1s # 刷新周期
        timeout-duration: 3s # 等待许可的超时时间
      jingyuAuth:
        limit-for-period: 5 # 认证接口限制更严格
        limit-refresh-period: 1s
        timeout-duration: 2s
  circuitbreaker:
    instances:
      jingyuSend:
        failure-rate-threshold: 50 # 失败率阈值50%
        slow-call-rate-threshold: 80 # 慢调用率阈值80%
        slow-call-duration-threshold: 5s # 慢调用时间阈值
        wait-duration-in-open-state: 30s # 熔断器打开状态持续时间
        sliding-window-size: 20 # 滑动窗口大小
        minimum-number-of-calls: 10 # 最小调用次数
        permitted-number-of-calls-in-half-open-state: 5 # 半开状态允许的调用次数
        automatic-transition-from-open-to-half-open-enabled: true
      jingyuAuth:
        failure-rate-threshold: 60 # 认证接口容错率稍高
        slow-call-rate-threshold: 70
        slow-call-duration-threshold: 10s
        wait-duration-in-open-state: 60s # 认证接口熔断时间更长
        sliding-window-size: 15
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        automatic-transition-from-open-to-half-open-enabled: true
```

## 使用方式

### 1. 自动集成
系统已自动集成Resilience4j功能，无需修改现有代码。所有通过`JingyuSendService.request()`方法的调用都会自动应用限流和熔断保护。

### 2. 监控接口

#### 获取所有状态
```bash
GET /admin/jingyu/resilience/status
```

#### 获取发送接口限流器状态
```bash
GET /admin/jingyu/resilience/send/ratelimiter
```

#### 获取发送接口熔断器状态
```bash
GET /admin/jingyu/resilience/send/circuitbreaker
```

#### 获取认证接口状态
```bash
GET /admin/jingyu/resilience/auth/ratelimiter
GET /admin/jingyu/resilience/auth/circuitbreaker
```

#### 重置熔断器（紧急情况）
```bash
POST /admin/jingyu/resilience/reset
```

#### 健康检查
```bash
GET /admin/jingyu/resilience/health
```

### 3. 响应码说明

- **429**: 请求被限流，需要稍后重试
- **503**: 服务被熔断，暂时不可用
- **500**: 系统内部错误

## 监控指标

### 限流器指标
- `availablePermissions`: 当前可用许可数
- `numberOfWaitingThreads`: 等待许可的线程数

### 熔断器指标
- `state`: 熔断器状态（CLOSED/OPEN/HALF_OPEN）
- `failureRate`: 失败率百分比
- `slowCallRate`: 慢调用率百分比
- `numberOfCalls`: 缓冲区中的调用总数
- `numberOfFailedCalls`: 失败调用数
- `numberOfSlowCalls`: 慢调用数
- `numberOfSuccessfulCalls`: 成功调用数

## 测试

运行测试类验证功能：

```bash
# 运行所有Resilience4j测试
mvn test -Dtest=JingyuResilienceTest

# 运行特定测试
mvn test -Dtest=JingyuResilienceTest#testRateLimiting
mvn test -Dtest=JingyuResilienceTest#testCircuitBreaker
```

## 最佳实践

### 1. 参数调优
- 根据实际业务量调整限流参数
- 根据下游服务性能调整熔断参数
- 定期监控指标并优化配置

### 2. 监控告警
- 设置限流触发告警
- 设置熔断器状态变化告警
- 监控失败率和慢调用率

### 3. 故障处理
- 限流时引导用户稍后重试
- 熔断时提供降级服务或友好提示
- 及时处理导致熔断的根本问题

## 故障排查

### 1. 限流问题
- 检查请求频率是否超过配置限制
- 查看`numberOfWaitingThreads`指标
- 考虑调整`limit-for-period`参数

### 2. 熔断问题
- 检查失败率和慢调用率
- 查看下游服务健康状态
- 必要时手动重置熔断器

### 3. 性能问题
- 监控响应时间
- 检查线程池配置
- 优化下游服务性能

## 注意事项

1. **配置变更**: 修改配置后需要重启应用
2. **监控频率**: 避免过于频繁的监控查询
3. **紧急重置**: 仅在确认问题解决后使用重置功能
4. **测试环境**: 在测试环境充分验证配置参数

## 联系方式

如有问题请联系开发团队或查看相关文档。
