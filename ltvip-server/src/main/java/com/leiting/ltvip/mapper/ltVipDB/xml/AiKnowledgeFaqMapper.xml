<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.leiting.ltvip.mapper.ltVipDB.AiKnowledgeFaqMapper">

    <select id="search" parameterType="com.leiting.ltvip.model.vo.AiKnowledgeVO" resultType="com.leiting.ltvip.model.po.AiKnowledgeFaq">
        select * from ai_knowledge_faq
        <where>
            <if test="keyword != null and keyword != ''">
                and (match (question, similar, answer) against (concat('+', #{keyword}) in boolean mode))
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
        </where>
        order by modify_date desc
    </select>

    <select id="selectExistingRecordIds" resultType="java.lang.String">
        select record_id from ai_knowledge_faq
        where record_id in
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </select>

</mapper>