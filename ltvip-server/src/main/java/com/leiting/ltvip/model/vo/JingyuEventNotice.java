package com.leiting.ltvip.model.vo;


import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 鲸域事件通知
 * <AUTHOR>
 * @date 2025/5/19 14:10
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class JingyuEventNotice {
    
    @JsonAlias("app_key")
    private String appKey; // 鲸域提供的AppKey
    
    private String token; // 本次请求的token
    
    private String nonce; // 随机字符串
    
    private String timestamp; // 回调时间戳
    
    @JsonAlias("encoding_content")
    private String encodingContent; // 加密后的回调内容（解密后是一个json结构的字符串）
    
    private String signature; // 签名
    
}
