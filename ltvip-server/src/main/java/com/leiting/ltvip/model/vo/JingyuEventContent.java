package com.leiting.ltvip.model.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 鲸域通知内容
 * <AUTHOR>
 * @date 2025/5/19 15:45
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class JingyuEventContent {
    
    private Integer eventType; // 事件类型
    private Integer msgType; // 接收群聊消息类型，消息类型：0 未知；1 文字；2 图文链接；3 图片 ；4 视频； 5语音 ；6 文件；7 好友名片；8 小程序；11 视频号消息；12 视频号直播消息；13 地理位置消息；14 转发的聊天记录；15 音视频通话消息；18 撤回消息；
    private String msgId; // 消息id
    private String senderId; // 发送者id
    private String receiverId; // 接收者id
    private String robotId; // 机器人id
    private String senderUnionId; // 外部联系人union_id
    private String senderExternalUserId; // 外部联系人external_user_id
    private String msgContent; // 消息内容：文字；图片（原图）；个人名片（base64加密后的好友信息）；小程序（base64加密后的json数据）；视频号（base64加密后的json数据）
    private String voiceTime; // 语音时长/视频时长
    private Integer senderType; // 消息发送者类型，type: 0 机器人；2 微信外部联系人；3 企业微信外部联系人
    private String title; // 文件/链接标题
    private String desc; // 链接描述
    private String href; // 链接URL，当消息为视频时，此处传视频的链接地址；当消息为文件时，此处传文件的链接地址；当消息为小程序时，此处传小程序的封面图
    private String msgTime; // 消息时间
    private String atList; // 当接收的消息中有被@的信息。展示被@的人员id，若多人被@，成员编号用逗号或分号隔开；当@全体成员时，该值为 All
    private String md5; // 接收的消息的md5
    private String channelMsgSn; // 消息编号，当msg_type消息类型为视频号消息时，可用该字段进行转发
    private String appInfo; // 消息appinfo
    private String quoteAppInfo; // 引用的消息appinfo,引用的消息appinfo,当引用消息时该字段有值,值为接收引用信息时返回的appinfo
    private Integer pvTag; // msg_type等于16才有值,音视频通话结束状态 1：接通 2:取消/拒绝 5:结束
    
}
