package com.leiting.ltvip.model.po;


import com.leiting.ltvip.model.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Table;


/**
 *
 * <AUTHOR>
 * @date 2025/4/7 13:48
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("AI知识库-FAQ")
@Table(name = "ai_knowledge_faq")
public class AiKnowledgeFaq extends BaseEntity implements AiKnowledgeRepeated {
    
    @ApiModelProperty(value = "记录ID", example = "")
    private String recordId;
    
    @ApiModelProperty(value = "问题", example = "问道手游有多少级？")
    private String question;
    
    @ApiModelProperty(value = "相似问，多个用|隔开", example = "问道有多少级？|问道游戏有多少级？")
    private String similar;
    
    @ApiModelProperty(value = "答案", example = "问道手游有xx级")
    private String answer;
    
    @ApiModelProperty(value = "操作人", example = "loveshes")
    private String operator;
    
    @Override
    public String getKeyword() {
        return question;
    }
    
}
