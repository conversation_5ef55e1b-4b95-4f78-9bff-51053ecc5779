package com.leiting.ltvip.model.response;

import com.leiting.ltvip.util.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 鲸域API响应结构
 * @param <T> data字段的类型
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@AllArgsConstructor
public class JingyuResponse<T> {
    private final int errcode;
    private final String errmsg;
    private final T data;

    /**
     * 转换为JSON字符串
     */
    public String toJson() {
        return GsonUtil.toJson(this);
    }

    /**
     * 创建成功响应
     */
    public static <T> JingyuResponse<T> success(T data) {
        return new JingyuResponse<>(0, "success", data);
    }

    /**
     * 创建失败响应
     */
    public static JingyuResponse<Object> fail(int errcode, String errmsg) {
        return new JingyuResponse<>(errcode, errmsg, null);
    }
}
