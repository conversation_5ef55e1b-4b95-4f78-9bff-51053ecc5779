package com.leiting.ltvip.aspect;

import com.leiting.ltvip.annotation.JingyuResilience;
import com.leiting.ltvip.model.response.JingyuResponse;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * 鲸域Resilience4j AOP切面
 * 自动为标记了@JingyuResilience注解的方法应用限流和熔断
 * <AUTHOR>
 * @date 2025/1/27
 */
@Aspect
@Component
public class JingyuResilienceAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(JingyuResilienceAspect.class);
    
    @Autowired
    private RateLimiterRegistry rateLimiterRegistry;
    
    @Autowired
    private CircuitBreakerRegistry circuitBreakerRegistry;
    
    @Around("@annotation(jingyuResilience)")
    public Object applyResilience(ProceedingJoinPoint joinPoint, JingyuResilience jingyuResilience) {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        try {
            // 构建执行逻辑
            Supplier<Object> supplier = () -> {
                try {
                    return joinPoint.proceed();
                } catch (Throwable throwable) {
                    throw new RuntimeException(throwable);
                }
            };
            
            // 应用限流
            if (jingyuResilience.enableRateLimit()) {
                RateLimiter rateLimiter = rateLimiterRegistry.rateLimiter(jingyuResilience.rateLimiter());
                supplier = RateLimiter.decorateSupplier(rateLimiter, supplier);
            }
            
            // 应用熔断
            if (jingyuResilience.enableCircuitBreaker()) {
                CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker(jingyuResilience.circuitBreaker());
                supplier = CircuitBreaker.decorateSupplier(circuitBreaker, supplier);
            }
            
            // 执行方法
            Object result = supplier.get();
            logger.debug("鲸域方法执行成功: {} args: {}", methodName, args);
            return result;
            
        } catch (RequestNotPermitted e) {
            // 限流异常处理
            logger.warn("鲸域方法被限流: {} args: {}", methodName, args);
            return JingyuResponse.fail(
                jingyuResilience.rateLimitErrorCode(), 
                jingyuResilience.rateLimitErrorMessage()
            ).toJson();
            
        } catch (CallNotPermittedException e) {
            // 熔断异常处理
            logger.warn("鲸域方法被熔断: {} args: {}", methodName, args);
            return JingyuResponse.fail(
                jingyuResilience.circuitBreakerErrorCode(), 
                jingyuResilience.circuitBreakerErrorMessage()
            ).toJson();
            
        } catch (Exception e) {
            // 其他异常处理
            logger.error("鲸域方法执行异常: {} args: {}", methodName, args, e);
            return JingyuResponse.fail(
                jingyuResilience.systemErrorCode(), 
                jingyuResilience.systemErrorMessage()
            ).toJson();
        }
    }
}
