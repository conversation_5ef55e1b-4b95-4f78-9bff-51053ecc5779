package com.leiting.ltvip.config;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * Resilience4j配置类 - 用于鲸域API的流量整型和熔断
 * <AUTHOR>
 * @date 2025/1/27
 */
@Configuration
public class Resilience4jConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(Resilience4jConfig.class);
    
    /**
     * 鲸域发送消息限流器配置
     * 用于平滑请求，避免瞬时大量请求
     */
    @Bean
    public RateLimiter jingyuSendRateLimiter() {
        RateLimiterConfig config = RateLimiterConfig.custom()
                .limitForPeriod(10) // 每秒允许10个请求
                .limitRefreshPeriod(Duration.ofSeconds(1)) // 每1秒刷新一次限制
                .timeoutDuration(Duration.ofSeconds(3)) // 等待获取许可的超时时间
                .build();
        
        RateLimiter rateLimiter = RateLimiter.of("jingyuSend", config);
        
        // 添加事件监听器
        rateLimiter.getEventPublisher()
                .onSuccess(event -> logger.debug("鲸域限流器 - 请求成功通过"))
                .onFailure(event -> logger.warn("鲸域限流器 - 请求被限流: {}", event.toString()));
        
        return rateLimiter;
    }
    
    /**
     * 鲸域发送消息熔断器配置
     * 用于在服务异常时快速失败，避免雪崩效应
     */
    @Bean
    public CircuitBreaker jingyuSendCircuitBreaker() {
        CircuitBreakerConfig config = CircuitBreakerConfig.custom()
                .failureRateThreshold(50) // 失败率达到50%时触发熔断
                .waitDurationInOpenState(Duration.ofSeconds(30)) // 熔断器打开状态持续30秒
                .slidingWindowSize(20) // 滑动窗口大小为20个请求
                .minimumNumberOfCalls(10) // 最少需要10个请求才开始计算失败率
                .slowCallRateThreshold(80) // 慢调用率阈值80%
                .slowCallDurationThreshold(Duration.ofSeconds(5)) // 超过5秒的调用被认为是慢调用
                .permittedNumberOfCallsInHalfOpenState(5) // 半开状态下允许5个测试请求
                .automaticTransitionFromOpenToHalfOpenEnabled(true) // 自动从打开状态转换到半开状态
                .build();
        
        CircuitBreaker circuitBreaker = CircuitBreaker.of("jingyuSend", config);
        
        // 添加事件监听器
        circuitBreaker.getEventPublisher()
                .onStateTransition(event -> 
                    logger.warn("鲸域熔断器状态变化: {} -> {}", 
                        event.getStateTransition().getFromState(), 
                        event.getStateTransition().getToState()))
                .onCallNotPermitted(event -> 
                    logger.warn("鲸域熔断器 - 请求被熔断拒绝"))
                .onError(event -> 
                    logger.error("鲸域熔断器 - 请求执行失败: {}", event.getThrowable().getMessage()))
                .onSuccess(event -> 
                    logger.debug("鲸域熔断器 - 请求执行成功，耗时: {}ms", event.getElapsedDuration().toMillis()));
        
        return circuitBreaker;
    }
    
    /**
     * 鲸域认证接口限流器配置
     * 认证接口调用频率较低，但需要保证稳定性
     */
    @Bean
    public RateLimiter jingyuAuthRateLimiter() {
        RateLimiterConfig config = RateLimiterConfig.custom()
                .limitForPeriod(5) // 每秒允许5个请求
                .limitRefreshPeriod(Duration.ofSeconds(1)) // 每1秒刷新一次限制
                .timeoutDuration(Duration.ofSeconds(2)) // 等待获取许可的超时时间
                .build();
        
        RateLimiter rateLimiter = RateLimiter.of("jingyuAuth", config);
        
        // 添加事件监听器
        rateLimiter.getEventPublisher()
                .onSuccess(event -> logger.debug("鲸域认证限流器 - 请求成功通过"))
                .onFailure(event -> logger.warn("鲸域认证限流器 - 请求被限流: {}", event.toString()));
        
        return rateLimiter;
    }
    
    /**
     * 鲸域认证接口熔断器配置
     * 认证接口的稳定性对整个系统至关重要
     */
    @Bean
    public CircuitBreaker jingyuAuthCircuitBreaker() {
        CircuitBreakerConfig config = CircuitBreakerConfig.custom()
                .failureRateThreshold(60) // 失败率达到60%时触发熔断（认证接口容错率稍高）
                .waitDurationInOpenState(Duration.ofSeconds(60)) // 熔断器打开状态持续60秒
                .slidingWindowSize(15) // 滑动窗口大小为15个请求
                .minimumNumberOfCalls(5) // 最少需要5个请求才开始计算失败率
                .slowCallRateThreshold(70) // 慢调用率阈值70%
                .slowCallDurationThreshold(Duration.ofSeconds(10)) // 超过10秒的调用被认为是慢调用
                .permittedNumberOfCallsInHalfOpenState(3) // 半开状态下允许3个测试请求
                .automaticTransitionFromOpenToHalfOpenEnabled(true) // 自动从打开状态转换到半开状态
                .build();
        
        CircuitBreaker circuitBreaker = CircuitBreaker.of("jingyuAuth", config);
        
        // 添加事件监听器
        circuitBreaker.getEventPublisher()
                .onStateTransition(event -> 
                    logger.warn("鲸域认证熔断器状态变化: {} -> {}", 
                        event.getStateTransition().getFromState(), 
                        event.getStateTransition().getToState()))
                .onCallNotPermitted(event -> 
                    logger.warn("鲸域认证熔断器 - 请求被熔断拒绝"))
                .onError(event -> 
                    logger.error("鲸域认证熔断器 - 请求执行失败: {}", event.getThrowable().getMessage()))
                .onSuccess(event -> 
                    logger.debug("鲸域认证熔断器 - 请求执行成功，耗时: {}ms", event.getElapsedDuration().toMillis()));
        
        return circuitBreaker;
    }
}
