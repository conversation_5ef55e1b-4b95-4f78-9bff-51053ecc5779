package com.leiting.ltvip.annotation;

import com.leiting.ltvip.constant.JingyuResilienceConst;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 鲸域Resilience4j注解
 * 用于标记需要应用限流和熔断的方法
 * 是否启用限流和熔断由JingyuURI的rateLimit字段决定
 * <AUTHOR>
 * @date 2025/1/27
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface JingyuResilience {

    /**
     * 限流器名称
     */
    String rateLimiter() default JingyuResilienceConst.DEFAULT_RATE_LIMITER;

    /**
     * 熔断器名称
     */
    String circuitBreaker() default JingyuResilienceConst.DEFAULT_CIRCUIT_BREAKER;
}
