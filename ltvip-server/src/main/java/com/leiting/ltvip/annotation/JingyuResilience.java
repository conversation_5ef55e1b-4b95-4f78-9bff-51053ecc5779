package com.leiting.ltvip.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 鲸域Resilience4j注解
 * 用于标记需要应用限流和熔断的方法
 * <AUTHOR>
 * @date 2025/1/27
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface JingyuResilience {
    
    /**
     * 限流器名称
     */
    String rateLimiter() default "jingyuSend";
    
    /**
     * 熔断器名称
     */
    String circuitBreaker() default "jingyuSend";
    
    /**
     * 是否启用限流
     */
    boolean enableRateLimit() default true;
    
    /**
     * 是否启用熔断
     */
    boolean enableCircuitBreaker() default true;
    
    /**
     * 限流错误码
     */
    int rateLimitErrorCode() default 429;
    
    /**
     * 限流错误信息
     */
    String rateLimitErrorMessage() default "请求过于频繁，请稍后重试";
    
    /**
     * 熔断错误码
     */
    int circuitBreakerErrorCode() default 503;
    
    /**
     * 熔断错误信息
     */
    String circuitBreakerErrorMessage() default "服务暂时不可用，请稍后重试";
    
    /**
     * 系统错误码
     */
    int systemErrorCode() default 500;
    
    /**
     * 系统错误信息
     */
    String systemErrorMessage() default "系统内部错误";
}
