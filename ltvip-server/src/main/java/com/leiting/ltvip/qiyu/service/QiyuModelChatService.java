package com.leiting.ltvip.qiyu.service;


import com.google.common.annotations.VisibleForTesting;
import com.google.common.reflect.TypeToken;
import com.leiting.config.api.anotation.LtValue;
import com.leiting.ltvip.constant.CommonConst;
import com.leiting.ltvip.constant.CommonConst.QiYu;
import com.leiting.ltvip.exception.CallModelException;
import com.leiting.ltvip.model.po.Tuple;
import com.leiting.ltvip.qiyu.common.ChatLogUtil;
import com.leiting.ltvip.qiyu.common.MD5Util;
import com.leiting.ltvip.qiyu.common.QiYuCheckSum;
import com.leiting.ltvip.qiyu.model.QiyuChatAnswer;
import com.leiting.ltvip.qiyu.model.QiyuChatEvent;
import com.leiting.ltvip.qiyu.model.QiyuChatQuestion;
import com.leiting.ltvip.service.VipUserInfoService;
import com.leiting.ltvip.service.api.FeiShuApiService;
import com.leiting.ltvip.service.api.SafeService;
import com.leiting.ltvip.service.bailian.BailianService;
import com.leiting.ltvip.service.bailian.ClassifierAgent;
import com.leiting.ltvip.service.bailian.EmotionAgent;
import com.leiting.ltvip.util.GsonUtil;
import com.leiting.ltvip.util.OkHttpPoolUtil;
import com.leiting.ltvip.util.QiyuSSEUtil;
import com.leiting.tools.base.annotation.Nullable;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 七鱼大模型问答服务
 * <a href="https://leiting.feishu.cn/docx/TDGhdRNHFoCCUQxcCQPcTaxDn7g">任务文档</a>
 * <a href="https://leiting.feishu.cn/wiki/NYyKwsj0QiumT7kc544crULwnyb">配置说明</a>
 * <AUTHOR>
 * @date 2025/3/25 15:04
 */
@Service
public class QiyuModelChatService {
    
    private static final Logger logger = LoggerFactory.getLogger(QiyuModelChatService.class);
    
    @Autowired
    private BailianService bailianService;
    
    @Autowired
    private EmotionAgent emotionAgent;
    
    @Autowired
    private ClassifierAgent classifierAgent;
    
    @Autowired
    private QiyuGroupService qiyuGroupService;
    
    @Autowired
    private VipUserInfoService vipUserInfoService;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Autowired
    private FeiShuApiService feiShuApiService;
    
    @Autowired
    private SafeService safeService;
    
    @LtValue(value = QiYu.QI_YU_API_APP_ID_CONFIG_KEY, defaultValue = QiYu.QI_YU_APP_KEY)
    private String qiyuAppKey; // 七鱼管理后台配置的appKey
    
    @LtValue(value = QiYu.QI_YU_API_APP_SECRET_CONFIG_KEY, defaultValue = QiYu.QI_YU_APP_SECRET)
    private String qiyuAppSecret; // 七鱼管理后台配置的appSecret
    
    private TriggerConfig qiyuSpamLimit;
    
    // 构建一个日志记录线程池
    private static final ExecutorService executor = Executors.newFixedThreadPool(20);
    
    @LtValue("qiyu.spam.limit") // 单会话中刷屏信息限频配置，json格式
    public void initQiyuSpamLimit(String json) {
        try {
            qiyuSpamLimit = GsonUtil.fromJson(json, TriggerConfig.class);
        } catch (Exception e) {
            logger.error("七鱼问答 初始化会话限频配置异常 json={}", json, e);
        }
    }
    
    private TriggerConfig modelUnmatchConfig;
    
    @LtValue("model.unmatch.config") // 大模型未匹配上的回答，用于判断是否为未知问题，message字段支持正则表达式
    public void initModelUnmatchConfig(String json) {
        try {
            modelUnmatchConfig = GsonUtil.fromJson(json, TriggerConfig.class);
        } catch (Exception e) {
            logger.error("七鱼问答 初始化大模型未匹配配置异常 json={}", json, e);
        }
    }
    
    private final Map<String, Double> MODEL_USE_RATE_MAP = new HashMap<>();
    private static final String MODEL_RATE_DEFAULT_KEY = "default"; // 大模型使用率默认key
    
    @LtValue("model.use.rate") // 调用大模型流量比例（其余则直接走人工），json数组
    public void initModelUseRate(String json) {
        try {
            List<ModelUseRate> list = GsonUtil.fromJson(json, new TypeToken<List<ModelUseRate>>() {
            }.getType());
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (ModelUseRate modelUseRate : list) {
                MODEL_USE_RATE_MAP.put(modelUseRate.getAppId(), modelUseRate.getRate());
            }
        } catch (Exception e) {
            logger.error("七鱼问答 初始化大模型使用率配置异常 json={}", json, e);
        }
    }
    
    @LtValue("model.suicide.message")
    private String suicideMessage; // 大模型触发轻生自动转人工的匹配词，配置为空则始终不触发
    
    private final Map<String, ModelEmotionConfig> MODEL_EMOTION_CONFIG_MAP = new HashMap<>();
    
    @LtValue("model.emotion.config") // 大模型情绪打分配置，json数组
    public void initModelEmotionConfig(String json) {
        try {
            List<ModelEmotionConfig> list = GsonUtil.fromJson(json, new TypeToken<List<ModelEmotionConfig>>() {
            }.getType());
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (ModelEmotionConfig config : list) {
                MODEL_EMOTION_CONFIG_MAP.put(config.getWorkspace(), config);
            }
        } catch (Exception e) {
            logger.error("七鱼问答 初始化大模型情绪打分配置异常 json={}", json, e);
        }
    }
    
    private final Map<String, HotQuestionConfig> HOT_QUESTION_CONFIG_MAP = new HashMap<>();
    
    @LtValue("model.hotQuestion.config") // 热点问题配置，json数组
    public void initModelHotQuestionConfig(String json) {
        try {
            List<HotQuestionConfig> list = GsonUtil.fromJson(json, new TypeToken<List<HotQuestionConfig>>() {
            }.getType());
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (HotQuestionConfig config : list) {
                HOT_QUESTION_CONFIG_MAP.put(config.getWorkspace(), config);
            }
        } catch (Exception e) {
            logger.error("七鱼问答 初始化大模型热点问题配置异常 json={}", json, e);
        }
    }
    
    @LtValue(value = "qiyu.transfer.tips", defaultValue = "您稍等，联系人工来协助处理您的问题。")
    private String qiYuTransferTips; // 七鱼触发转人工引导语
    
    private static final String URI_CHAT_CALLBACK = "robot/llm/callback"; // 机器人问答回调接口
    
    public static final Integer ANSWER_TYPE_NA = -1; // GPT不适用
    public static final Integer ANSWER_TYPE_UNMATCH = 0; // GPT未匹配
    public static final Integer ANSWER_TYPE_MATCHED = 2; // GPT已匹配
    
    // 轻生类型
    public static final Integer SUICIDE_TYPE_NA = -1; // 不适用
    public static final Integer SUICIDE_TYPE_NO = 0; // 无轻生
    public static final Integer SUICIDE_TYPE_YES = 1; // 有轻生想法
    
    // 涉政类型
    public static final Integer POLITICAL_TYPE_NA = -1; // 不适用（请求失败等）
    public static final Integer POLITICAL_TYPE_NO = 0; // 不涉政
    public static final Integer POLITICAL_TYPE_YES = 1; // 涉政
    
    public static final Integer TRANS_TYPE_NONE = 0; // 无转接
    public static final Integer TRANS_TYPE_MANUAL = 1; // 转人工客服
    
    private static final String MESSAGE_TYPE_ANSWER = "ANSWER"; // 回复消息
    private static final String MESSAGE_TYPE_TRANSFER = "TRANSFER_AGENT"; // 转人工客服
    
    private static final String CACHE_KEY_QUESTION_RECENT = "qiyu:question:recent:"; // 会话内最近一次问答的key
    private static final String CACHE_KEY_QUESTION_COUNT = "qiyu:question:count:"; // 会话内重复问答次数的key
    private static final String CACHE_KEY_QUESTION_EMOTION = "bailian:answer:emotion:"; // 会话内情绪得分的key
    private static final String CACHE_KEY_ANSWER_UNMATCH = "bailian:answer:unmatch:"; // 大模型未匹配上次数的key
    private static final String CACHE_KEY_SESSION_QUESTION = "qiyu:session:question:"; // 会话内历史问题的key
    private static final String CACHE_KEY_TAG_COUNT = "qiyu:tag:count:"; // 问题tag数量的key
    
    // 异步回调七鱼
    public void chatCallback(String workspace, String bailianAppId, QiyuChatQuestion chatQuestion) {
        QiyuChatAnswer chatAnswer;
        Integer matchType = ANSWER_TYPE_NA; // 默认GPT不适用
        Context context = new Context();
        boolean isVip = vipUserInfoService.isVip(workspace, chatQuestion.getForeignId()); // 判断是否为vip
        if (isVip) { // vip直接走人工客服
            chatAnswer = callStaff(workspace, bailianAppId, chatQuestion, qiYuTransferTips, true);
            ChatLogUtil.info("回调输出 转人工 VIP chatQuestion={}", chatQuestion);
        } else { // 非vip
            if (isSpamLimit(chatQuestion)) { // 刷屏限制
                chatAnswer = callSpamLimit(chatQuestion);
            } else {
                try {
                    boolean triggered = triggerTransferStaff(workspace, chatQuestion, context);
                    if (triggered) { // 问题触发转人工
                        chatAnswer = callStaff(workspace, bailianAppId, chatQuestion, qiYuTransferTips, false);
                        ChatLogUtil.info("回调输出 转人工 问题触发 chatQuestion={}", chatQuestion);
                    } else if (useModel(bailianAppId)) {  // 灰度策略
                        chatAnswer = callModel(workspace, bailianAppId, chatQuestion, context);
                        matchType = matchAnswer(chatAnswer.getAnswer()) ? ANSWER_TYPE_MATCHED : ANSWER_TYPE_UNMATCH;
                    } else { // 未灰度到的走人工
                        chatAnswer = callStaff(workspace, bailianAppId, chatQuestion, qiYuTransferTips, false);
                        ChatLogUtil.info("回调输出 转人工 未灰度到 chatQuestion={}", chatQuestion);
                    }
                } catch (CallModelException e) { // 调用大模型异常则转人工
                    chatAnswer = callStaff(workspace, bailianAppId, chatQuestion, bailianService.getExceptionMessage(), false);
                    ChatLogUtil.info("回调输出 转人工 模型异常 chatQuestion={}", chatQuestion);
                }
            }
        }
        
        String data = GsonUtil.toJson(chatAnswer);
        String time = String.valueOf(System.currentTimeMillis() / 1000);
        String checksum = QiYuCheckSum.encode(qiyuAppSecret, MD5Util.md5(data), time);
        // 请求七鱼服务器
        String url = QiYu.QI_YU_URL_BASE + URI_CHAT_CALLBACK + "?appKey=" + qiyuAppKey + "&time=" + time + "&checksum=" + checksum;
        try {
            String response = OkHttpPoolUtil.postJson(url, data);
            ChatLogUtil.info("回调输出 响应结果 url={} \nrequest={} response={}", url, data, response);
            // 解析返回值
            Map<String, Object> result = GsonUtil.jsonToMap(response);
            if (result != null) {
                Object code = result.get(CommonConst.RESPONSE_CONST.CODE);
                if (code == null || Integer.parseInt(String.valueOf(code)) != QiYu.SUCCESS) {
                    logger.error("七鱼问答 回调输出 响应失败 chatQuestion={}, response={}", chatQuestion, response);
                }
            }
        } catch (Exception e) {
            logger.error("七鱼问答 回调输出 请求异常 chatQuestion={}", chatQuestion, e);
        }
        
        // 本来就是异步的，这里最后记录日志
        Tuple<Integer, List<Map<String, String>>> tuple = safeService.queryQiyuPoliticalType(chatAnswer.getAnswer());
        Integer politicalType = tuple.getFirst();
        if (POLITICAL_TYPE_YES.equals(politicalType)) { // 政治敏感发送飞书预警
            feiShuApiService.sendAiAnswerPoliticalWarning(workspace, bailianAppId, chatQuestion, chatAnswer.getAnswer(), tuple.getSecond());
        }
        ChatLogUtil.log(workspace, bailianAppId, chatQuestion, chatAnswer, matchType, politicalType, context);
    }
    
    // 刷屏限制
    public QiyuChatAnswer callSpamLimit(QiyuChatQuestion chatQuestion) {
        return QiyuChatAnswer.builder().robotId(chatQuestion.getRobotId())
                             .sourceType(chatQuestion.getSourceType())
                             .sessionId(chatQuestion.getSessionId())
                             .foreignId(chatQuestion.getForeignId())
                             .question(chatQuestion.getQuestion())
                             .questionUid(chatQuestion.getQuestionUid())
                             .answer(qiyuSpamLimit.getMessage())
                             .answerType(ANSWER_TYPE_MATCHED)
                             .transType(TRANS_TYPE_NONE)
                             .build();
    }
    
    // 直接转接人工客服
    private QiyuChatAnswer callStaff(String workspace, String bailianAppId, QiyuChatQuestion chatQuestion, String transferTips, boolean isVip) {
        return QiyuChatAnswer.builder()
                             .robotId(chatQuestion.getRobotId())
                             .sourceType(chatQuestion.getSourceType())
                             .sessionId(chatQuestion.getSessionId())
                             .foreignId(chatQuestion.getForeignId())
                             .question(chatQuestion.getQuestion())
                             .questionUid(chatQuestion.getQuestionUid())
                             .answer(transferTips)
                             .answerType(ANSWER_TYPE_MATCHED)
                             .transType(TRANS_TYPE_MANUAL)
                             .groupId(qiyuGroupService.transferGroupId(workspace, bailianAppId, isVip))
                             .build();
    }
    
    // 请求大模型
    private QiyuChatAnswer callModel(String workspace, String bailianAppId, QiyuChatQuestion chatQuestion, Context context) {
        // 请求大模型
        Long sessionId = chatQuestion.getSessionId();
        String answer = bailianService.chat(workspace, bailianAppId, sessionId, chatQuestion.getQuestion());
        // 封装请求七鱼参数
        QiyuChatAnswer chatAnswer = QiyuChatAnswer.builder()
                                                  .robotId(chatQuestion.getRobotId())
                                                  .sourceType(chatQuestion.getSourceType())
                                                  .sessionId(sessionId)
                                                  .foreignId(chatQuestion.getForeignId())
                                                  .question(chatQuestion.getQuestion())
                                                  .questionUid(chatQuestion.getQuestionUid())
                                                  .answer(answer)
                                                  .build();
        // 判断是否需要转接客服
        if (answerTransferStaff(sessionId, answer, context)) { // 转人工
            chatAnswer.setAnswerType(ANSWER_TYPE_UNMATCH);
            chatAnswer.setTransType(TRANS_TYPE_MANUAL);
            chatAnswer.setGroupId(qiyuGroupService.transferGroupId(workspace, bailianAppId, false));
            ChatLogUtil.info("回调输出 转人工 回复触发 chatQuestion={}", chatQuestion);
        } else {
            chatAnswer.setAnswerType(ANSWER_TYPE_MATCHED);
            chatAnswer.setTransType(TRANS_TYPE_NONE);
        }
        return chatAnswer;
    }
    
    
    // 流式响应七鱼
    public void chatStream(SseEmitter emitter, String workspace, String bailianAppId, QiyuChatQuestion chatQuestion) {
        try {
            Long sessionId = chatQuestion.getSessionId();
            Context context = new Context();
            // 发送chat.start事件
            QiyuSSEUtil.sendChatStart(emitter, sessionId);
            ChatLogUtil.info("流式输出 会话开始 chatQuestion={}", chatQuestion);
            
            boolean isVip = vipUserInfoService.isVip(workspace, chatQuestion.getForeignId()); // 判断是否为vip
            if (isVip) { // vip直接走人工客服
                sendStaff(emitter, workspace, bailianAppId, chatQuestion, qiYuTransferTips, true, context);
                ChatLogUtil.info("流式输出 转人工 VIP chatQuestion={}", chatQuestion);
            } else { // 非vip
                if (isSpamLimit(chatQuestion)) { // 刷屏限制
                    sendSpamLimit(emitter, workspace, bailianAppId, chatQuestion, context);
                } else {
                    try {
                        boolean triggered = triggerTransferStaff(workspace, chatQuestion, context);
                        if (triggered) { // 触发转人工
                            sendStaff(emitter, workspace, bailianAppId, chatQuestion, qiYuTransferTips, false, context);
                            ChatLogUtil.info("流式输出 转人工 问题触发 chatQuestion={}", chatQuestion);
                        } else if (useModel(bailianAppId)) { // 灰度策略
                            sendModel(emitter, workspace, bailianAppId, chatQuestion, context);
                        } else { // 未灰度到的走人工
                            sendStaff(emitter, workspace, bailianAppId, chatQuestion, qiYuTransferTips, false, context);
                            ChatLogUtil.info("流式输出 转人工 未灰度到 chatQuestion={}", chatQuestion);
                        }
                    } catch (CallModelException e) { // 调用大模型异常则转人工
                        sendStaff(emitter, workspace, bailianAppId, chatQuestion, bailianService.getExceptionMessage(), false, context);
                        ChatLogUtil.info("流式输出 转人工 模型异常 chatQuestion={}", chatQuestion);
                    }
                }
            }
            
            // 发送chat.completed事件
            QiyuSSEUtil.sendChatCompleted(emitter, sessionId);
            
            // 发送done事件
            QiyuSSEUtil.sendDone(emitter);
            ChatLogUtil.info("流式输出 会话完成 chatQuestion={} done", chatQuestion);
        } catch (Exception e) {
            logger.error("七鱼问答 流式输出 发送事件异常 appId={} question={}", bailianAppId, chatQuestion, e);
            QiyuSSEUtil.close(emitter, e);
        } finally {
            QiyuSSEUtil.close(emitter);
        }
    }
    
    // 刷屏限制
    public void sendSpamLimit(SseEmitter emitter, String workspace, String bailianAppId, QiyuChatQuestion chatQuestion, Context context) {
        // 发送message.completed事件（固定提示语）
        QiyuChatEvent limit = QiyuChatEvent.builder()
                                           .createAt(System.currentTimeMillis())
                                           .sessionId(chatQuestion.getSessionId())
                                           .type(MESSAGE_TYPE_ANSWER)
                                           .content(qiyuSpamLimit.getMessage())
                                           .transType(TRANS_TYPE_NONE)
                                           .build();
        QiyuSSEUtil.sendMessageDelta(emitter, limit); // 限频提示需要发 message.delta 事件
        QiyuSSEUtil.sendMessageCompleted(emitter, limit);
        ChatLogUtil.info("流式输出 限频提示 chatQuestion={}", chatQuestion);
        // 记录日志
        ChatLogUtil.log(workspace, bailianAppId, chatQuestion, qiyuSpamLimit.getMessage(), ANSWER_TYPE_NA, null, POLITICAL_TYPE_NA, context);
    }
    
    // 直接转接人工客服
    private void sendStaff(SseEmitter emitter, String workspace, String bailianAppId, QiyuChatQuestion chatQuestion, String transferTips, boolean isVip,
                           Context context) {
        // 发送message.completed事件（转人工）
        Long groupId = qiyuGroupService.transferGroupId(workspace, bailianAppId, isVip);
        QiyuChatEvent transfer = QiyuChatEvent.builder()
                                              .createAt(System.currentTimeMillis())
                                              .sessionId(chatQuestion.getSessionId())
                                              .type(MESSAGE_TYPE_TRANSFER)
                                              .content(transferTips)
                                              .transType(TRANS_TYPE_MANUAL)
                                              .groupId(groupId)
                                              .build();
        QiyuSSEUtil.sendMessageCompleted(emitter, transfer);
        // 记录日志
        ChatLogUtil.log(workspace, bailianAppId, chatQuestion, transferTips, ANSWER_TYPE_NA, groupId, POLITICAL_TYPE_NA, context);
    }
    
    // 请求大模型
    private void sendModel(SseEmitter emitter, String workspace, String bailianAppId, QiyuChatQuestion chatQuestion, Context context) {
        Long sessionId = chatQuestion.getSessionId();
        
        // 大模型处理过程
        StringBuilder fullAnswer = new StringBuilder();
        Long groupId;
        bailianService.chatStream(workspace, bailianAppId, sessionId, chatQuestion.getQuestion()).blockingForEach(result -> { // 处理增量消息
                    // 发送message.delta事件
                    String text = result.getOutput().getText();
                    fullAnswer.append(text);
                    QiyuChatEvent delta = QiyuChatEvent.builder()
                                                       .createAt(System.currentTimeMillis())
                                                       .sessionId(sessionId)
                                                       .type(MESSAGE_TYPE_ANSWER)
                                                       .content(text)
                                                       .build();
                    QiyuSSEUtil.sendMessageDelta(emitter, delta);
                }
        );
        
        // 发送message.completed事件
        if (answerTransferStaff(sessionId, fullAnswer.toString(), context)) { // 转人工
            groupId = qiyuGroupService.transferGroupId(workspace, bailianAppId, false);
            QiyuChatEvent transfer = QiyuChatEvent.builder()
                                                  .createAt(System.currentTimeMillis())
                                                  .sessionId(sessionId)
                                                  .type(MESSAGE_TYPE_TRANSFER)
                                                  .content(fullAnswer.toString())
                                                  .transType(TRANS_TYPE_MANUAL)
                                                  .groupId(groupId)
                                                  .build();
            QiyuSSEUtil.sendMessageCompleted(emitter, transfer);
            ChatLogUtil.info("流式输出 转人工 回复触发 chatQuestion={} text={}", chatQuestion, fullAnswer);
        } else {  // 消息完成
            groupId = null;  // 不转接客服
            QiyuChatEvent messageCompleted = QiyuChatEvent.builder()
                                                          .createAt(System.currentTimeMillis())
                                                          .sessionId(sessionId)
                                                          .type(MESSAGE_TYPE_ANSWER)
                                                          .content(fullAnswer.toString())
                                                          .build();
            QiyuSSEUtil.sendMessageCompleted(emitter, messageCompleted);
            ChatLogUtil.info("流式输出 消息完成 chatQuestion={} text={}", chatQuestion, fullAnswer);
        }
        
        // 这里记录日志还是要异步，优先保证大模型结果第一时间返回
        Integer matchType = matchAnswer(fullAnswer.toString()) ? ANSWER_TYPE_MATCHED : ANSWER_TYPE_UNMATCH;
        executor.execute(() -> {
            Tuple<Integer, List<Map<String, String>>> tuple = safeService.queryQiyuPoliticalType(fullAnswer.toString());
            Integer politicalType = tuple.getFirst();
            if (POLITICAL_TYPE_YES.equals(politicalType)) { // 政治敏感发送飞书预警
                feiShuApiService.sendAiAnswerPoliticalWarning(workspace, bailianAppId, chatQuestion, fullAnswer.toString(), tuple.getSecond());
            }
            ChatLogUtil.log(workspace, bailianAppId, chatQuestion, fullAnswer.toString(), matchType, groupId, politicalType, context);
        });
    }
    
    // 判断是否刷屏
    public boolean isSpamLimit(QiyuChatQuestion chatQuestion) {
        if (!TriggerConfig.isValid(qiyuSpamLimit)) {
            return false; // 未配置限频规则，不限制刷屏
        }
        
        String recentKey = CACHE_KEY_QUESTION_RECENT + chatQuestion.getSessionId();
        String countKey = CACHE_KEY_QUESTION_COUNT + chatQuestion.getSessionId();
        BoundValueOperations<String, String> recentOp = redisTemplate.boundValueOps(recentKey);
        BoundValueOperations<String, String> countOp = redisTemplate.boundValueOps(countKey);
        // 会话内第一次发送消息，不触发限制
        if (recentOp.get() == null) {
            // 记录最近一次发送消息内容，重复消息计数
            recentOp.set(chatQuestion.getQuestion(), qiyuSpamLimit.getCacheSecond(), TimeUnit.SECONDS);
            countOp.increment(1L);
            countOp.expire(qiyuSpamLimit.getCacheSecond(), TimeUnit.SECONDS);
            return false;
        }
        
        if (StringUtils.equals(recentOp.get(), chatQuestion.getQuestion())) {
            // 会话内发送重复内容，判断是否触发次数（到这里起码是第2次了）
            Long count = Optional.ofNullable(countOp.increment(1L)).orElse(2L);
            countOp.expire(qiyuSpamLimit.getCacheSecond(), TimeUnit.SECONDS);
            return count >= qiyuSpamLimit.getTriggerNum();
        } else {
            // 会话内发送新内容，则记录最近一次发送消息内容，重置会话内重复消息统计计数
            recentOp.set(chatQuestion.getQuestion(), qiyuSpamLimit.getCacheSecond(), TimeUnit.SECONDS);
            countOp.set("1", qiyuSpamLimit.getCacheSecond(), TimeUnit.SECONDS);
            return false;
        }
    }
    
    // 问题触发转人工
    private boolean triggerTransferStaff(String workspace, QiyuChatQuestion chatQuestion, Context context) {
        return hotQuestionTrigger(workspace, chatQuestion, context) || emotionTrigger(workspace, chatQuestion, context);
    }
    
    // 大模型回复触发转人工
    private boolean answerTransferStaff(Long sessionId, String answer, Context context) {
        return suicideTrigger(answer, context) || unmatchAnswerTrigger(sessionId, answer);
    }
    
    // 回答未匹配上触发次数
    private boolean unmatchAnswerTrigger(Long sessionId, String answer) {
        if (!TriggerConfig.isValid(modelUnmatchConfig)) {
            return false; // 未配置默认认为大模型输出准确
        }
        
        boolean match = matchAnswer(answer);
        String unmatchKey = CACHE_KEY_ANSWER_UNMATCH + sessionId;
        if (match) {
            redisTemplate.delete(unmatchKey); // 重置计数
            return false; // 大模型输出准确
        }
        BoundValueOperations<String, String> ops = redisTemplate.boundValueOps(unmatchKey);
        Long count = Optional.ofNullable(ops.increment(1L)).orElse(1L);
        ops.expire(modelUnmatchConfig.getCacheSecond(), TimeUnit.SECONDS);
        return count >= modelUnmatchConfig.getTriggerNum();
    }
    
    // 有轻生想法立即转人工
    private boolean suicideTrigger(String answer, Context context) {
        if (StringUtils.isBlank(suicideMessage)) {
            return false; // 配置为空则始终不触发
        }
        boolean isSuicide = StringUtils.contains(answer, suicideMessage);
        context.setSuicideType(isSuicide ? SUICIDE_TYPE_YES : SUICIDE_TYPE_NO);
        return isSuicide;
    }
    
    // 情绪得分累计触发转人工
    @VisibleForTesting
    protected boolean emotionTrigger(String workspace, QiyuChatQuestion chatQuestion, Context context) {
        ModelEmotionConfig config = MODEL_EMOTION_CONFIG_MAP.get(workspace);
        if (!ModelEmotionConfig.isValid(config)) {
            logger.error("七鱼问答 未配置大模型情绪打分配置 workspace={} config={}", workspace, config);
            return false; // 未配置则不触发
        }
        // 获取情绪得分
        int score = emotionAgent.action(workspace, config.getAppId(), chatQuestion.getQuestion());
        context.setScore(score); // 用于记录日志
        // 计算会话内累计情绪得分
        String key = CACHE_KEY_QUESTION_EMOTION + chatQuestion.getSessionId();
        BoundValueOperations<String, String> ops = redisTemplate.boundValueOps(key);
        ops.expire(config.getCacheSecond(), TimeUnit.SECONDS); // 设置过期时间，需要大于七鱼会话时间
        Long totalScore = Optional.ofNullable(ops.increment(score)).orElse((long) score);
        boolean trigger = totalScore >= config.getTransferScore();
        if (trigger) { // 转人工后重置计数
            redisTemplate.delete(key);
        }
        return trigger;
    }
    
    // 热点问题累计触发转人工
    @VisibleForTesting
    protected boolean hotQuestionTrigger(String workspace, QiyuChatQuestion chatQuestion, Context context) {
        HotQuestionConfig config = HOT_QUESTION_CONFIG_MAP.get(workspace);
        if (!HotQuestionConfig.isValid(config)) {
            logger.error("七鱼问答 未配置大模型热点问题配置 workspace={} config={}", workspace, config);
            return false; // 未配置则不触发
        }
        // 存储当前会话
        pushSessionQuestion(chatQuestion.getSessionId(), chatQuestion.getQuestion(), config.getQuestionCacheSecond());
        // 查询当前会话累计问题
        List<String> questions = querySessionQuestions(chatQuestion.getSessionId());
        if (config.getStartQuestion() != null && questions.size() < config.getStartQuestion()) {
            return false; // 未达到起问个数
        }
        if (config.getEndQuestion() != null && questions.size() > config.getEndQuestion()) {
            return false; // 已超过结束问题个数
        }
        // 查询当前问题所属tag
        Pair<String, String> tags = classifierAgent.action(workspace, config.getAppId(), String.join("|", questions));
        String tag = formatTag(tags);
        context.setTag(tag); // 用于记录日志
        // 查询当前tag累计次数
        Long count = incrementTagCount(workspace, tag, config.getTagCacheSecond());
        if (count >= config.getWarningCount()) {
            feiShuApiService.sendHotQuestionWarning(workspace, chatQuestion, config.getTagCacheSecond(), tag, count);
        }
        return count >= config.getTransferCount(); // 触发转人工
    }
    
    // 查询当前会话累计问题
    private List<String> querySessionQuestions(Long sessionId) {
        String key = CACHE_KEY_SESSION_QUESTION + sessionId;
        return Optional.ofNullable(redisTemplate.opsForList().range(key, 0, -1)).orElse(new ArrayList<>());
    }
    
    // 存储当前会话
    private void pushSessionQuestion(Long sessionId, String question, Integer cacheSecond) {
        String key = CACHE_KEY_SESSION_QUESTION + sessionId;
        redisTemplate.opsForList().rightPush(key, question);
        redisTemplate.expire(key, cacheSecond, TimeUnit.SECONDS);
    }
    
    // 自增当前tag累计次数
    private Long incrementTagCount(String workspace, String tag, Integer cacheSecond) {
        if (StringUtils.isAnyBlank(workspace, tag)) {
            return 0L;
        }
        String key = CACHE_KEY_TAG_COUNT + workspace + ":" + tag;
        BoundValueOperations<String, String> ops = redisTemplate.boundValueOps(key);
        ops.expire(cacheSecond, TimeUnit.SECONDS);
        return Optional.ofNullable(ops.increment(1L)).orElse(1L);
    }
    
    // 格式化问题分类标签
    private String formatTag(Pair<String, String> tags) {
        if (tags == null || StringUtils.isBlank(tags.getLeft())) {
            return "";
        }
        String tag = tags.getLeft();
        if (StringUtils.isNotBlank(tags.getRight())) {
            tag += "-" + tags.getRight();
        }
        return tag;
    }
    
    // 当前回答是否匹配上
    private boolean matchAnswer(String answer) {
        if (StringUtils.isBlank(answer)) {
            return false;
        }
        Pattern pattern = Pattern.compile(modelUnmatchConfig.getMessage());
        return !pattern.matcher(answer).find();
    }
    
    // 判断当前是否使用大模型回复
    @VisibleForTesting
    protected boolean useModel(String bailianAppId) {
        Double rate = MODEL_USE_RATE_MAP.get(bailianAppId);
        if (rate == null) { // 未单独配置则走默认
            rate = MODEL_USE_RATE_MAP.getOrDefault(MODEL_RATE_DEFAULT_KEY, 0.0);
        }
        return Math.random() <= rate;
    }
    
    // 触发限制配置
    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    private static final class TriggerConfig {
        private Integer triggerNum; // 触发限制次数
        private Integer cacheSecond; // 缓存时间（需要大于七鱼会话时间）
        private String message; // 提示信息
        
        // 是否有效
        public static boolean isValid(TriggerConfig limit) {
            return limit != null
                           && (limit.getTriggerNum() != null && limit.getTriggerNum() > 0)
                           && (limit.getCacheSecond() != null && limit.getCacheSecond() > 0)
                           && StringUtils.isNotBlank(limit.getMessage());
        }
        
    }
    
    // 调用大模型流量配置
    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    private static final class ModelUseRate {
        private String appId; // 百炼应用appId
        private Double rate; // 调用大模型流量占比
        private String desc; // 备注信息
    }
    
    // 大模型情绪打分配置
    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    private static final class ModelEmotionConfig {
        private String workspace; // 工作空间，与七鱼后台链接需要配置一致
        private String appId; // 百炼应用appId
        private Integer cacheSecond; // 缓存时间（需要大于七鱼会话时间）
        private Integer transferScore; // 一场会话中触发转人工的累计分值
        
        // 是否有效
        public static boolean isValid(ModelEmotionConfig config) {
            return config != null
                           && StringUtils.isNotBlank(config.getWorkspace())
                           && StringUtils.isNotBlank(config.getAppId())
                           && config.getCacheSecond() != null && config.getCacheSecond() > 0
                           && config.getTransferScore() != null && config.getTransferScore() > 0;
        }
    }
    
    // 热点问题配置
    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    private static final class HotQuestionConfig {
        private String workspace; // 工作空间，与七鱼后台链接需要配置一致
        private String appId; // 百炼应用appId
        private Integer questionCacheSecond; // 会话问题缓存时间（需要大于七鱼会话时间）
        private Integer tagCacheSecond; // 问题标签缓存时间
        @Nullable
        private Integer startQuestion; // 第xx个问题开始触发请求问题分类(包含端点值)，为空表示第1个问题就开始请求问题分类
        @Nullable
        private Integer endQuestion; // 第xx个问题结束触发请求问题分类(包含端点值)，为空表示一直到会话结束都会请求问题分类
        private Integer warningCount; // 热点问题触发预警的累计个数
        private Integer transferCount; // 热点问题触发转人工的累计个数
        
        // 是否有效
        public static boolean isValid(HotQuestionConfig config) {
            return config != null
                           && StringUtils.isNotBlank(config.getWorkspace())
                           && StringUtils.isNotBlank(config.getAppId())
                           && config.getQuestionCacheSecond() != null && config.getQuestionCacheSecond() > 0
                           && config.getTagCacheSecond() != null && config.getTagCacheSecond() > 0
                           && config.getWarningCount() != null && config.getWarningCount() > 0
                           && config.getTransferCount() != null && config.getTransferCount() > 0;
        }
    }
    
    // 判断过程中的中间信息
    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static final class Context {
        private Integer score; // 情绪得分
        private String tag; // 问题分类
        private Integer suicideType; // 轻生类型
    }
    
}
