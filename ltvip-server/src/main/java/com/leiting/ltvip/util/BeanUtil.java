package com.leiting.ltvip.util;

import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class BeanUtil {
    private static final Logger logger = LoggerFactory.getLogger(BeanUtil.class);

    public static Map<Class<?>, Map<String, Field>> mapFields = new HashMap();

    /**
     * 忽略字段名称大小写Copy，基础类型Copy可自适应转型
     * @param source
     * @param type
     * @param ignoreColumns
     * @param <T>
     * @return
     */
    public static <T> T copyObject(Object source, Class<T> type, String... ignoreColumns) {
        if (source == null) {
            return null;
        } else {
            try {
                T newInstance = type.newInstance();
                Map<String, Field> sourceDeclaredFields = initFileMap(source.getClass());
                Map<String, Field> targetDeclaredFields = initFileMap(type);
                Iterator iterator = sourceDeclaredFields.entrySet().iterator();

                while(iterator.hasNext()) {
                    Map.Entry<String, Field> entry = (Map.Entry)iterator.next();
                    boolean skip = false;
                    if (ignoreColumns != null) {
                        String[] arr = ignoreColumns;
                        int len = ignoreColumns.length;

                        for(int i = 0; i < len; ++i) {
                            String ignoreColumn = arr[i];
                            if (((String)entry.getKey()).equalsIgnoreCase(ignoreColumn)) {
                                skip = true;
                                break;
                            }
                        }
                    }

                    if (!skip) {
                        ((Field)entry.getValue()).setAccessible(true);
                        Object fieldVal = ReflectionUtils.getField((Field)entry.getValue(), source);
                        ((Field)entry.getValue()).setAccessible(false);
                        if (fieldVal != null) {
                            Field targetField = (Field)targetDeclaredFields.get(entry.getKey());
                            if (targetField != null) {
                                targetField.setAccessible(true);
                                ReflectionUtils.setField(targetField, newInstance, ConvertUtils.convert(fieldVal, targetField.getType()));
                                targetField.setAccessible(false);
                            }
                        }
                    }
                }

                return newInstance;
            } catch (Exception e) {
                logger.error("无法解析映射, type:" + type.getName() + ", source:" + source, e);
                return null;
            }
        }
    }

    public static <T> List<T> copyList(List<?> source, Class<T> type, String... ignoreColumns) {
        if (source != null && !source.isEmpty()) {
            ArrayList resultList = new ArrayList(source.size());

            try {
                Iterator iterator = source.iterator();

                while(iterator.hasNext()) {
                    Object obj = iterator.next();
                    T copyObject = copyObject(obj, type);
                    if (copyObject != null) {
                        resultList.add(copyObject);
                    }
                }
            } catch (Exception e) {
                logger.error("无法解析映射, type:" + type.getName(), e);
            }

            return resultList;
        } else {
            return null;
        }
    }

    public static Map<String, Field> initFileMap(Class<?> type) {
        Map<String, Field> targetDeclaredFields = (Map)mapFields.get(type);
        if (targetDeclaredFields == null) {
            for(targetDeclaredFields = new HashMap(); !type.equals(Object.class); type = type.getSuperclass()) {
                Field[] declaredFields2 = type.getDeclaredFields();
                Field[] arr$ = declaredFields2;
                int len$ = declaredFields2.length;

                for(int i$ = 0; i$ < len$; ++i$) {
                    Field field = arr$[i$];
                    ((Map)targetDeclaredFields).put(field.getName().toLowerCase(), field);
                }
            }

            mapFields.put(type, targetDeclaredFields);
        }

        return (Map)targetDeclaredFields;
    }

    /**
     * 判断对象是否为空，且对象的指定属性是否都不为空
     * @param obj
     * @param fieldList 指定属性集合
     * @return
     * @throws Exception
     */
    public static boolean specificFieldsIsNotNull(Object obj, String... fieldList) {
        try {
            if (obj == null) {
                return false;
            }

            Map<String, Field> fieldsMap = getFieldsForMap(obj);
            for (String fieldName : fieldList) {
                Field field = fieldsMap.get(fieldName);
                field.setAccessible(true); // 设置属性是可以访问的(私有的也可以)
                if(Collection.class.isAssignableFrom(field.getType())) {
                    Collection val = (Collection) field.get(obj);
                    if(val == null || val.size() == 0) {
                        return false;
                    }
                }
                if(Map.class.isAssignableFrom(field.getType())) {
                    Map val = (Map) field.get(obj);
                    if(val == null || val.size() == 0) {
                        return false;
                    }
                }
                if(String.class.isAssignableFrom(field.getType())) {
                    String val = (String) field.get(obj);
                    if(StringUtils.isEmpty(val)) {
                        return false;
                    }
                }
                Object val = field.get(obj);
                if(val == null) {
                    return false;
                }
            }
            fieldsMap.clear();
        }
        catch (Exception e) {
            logger.error("specificFieldsIsNotNull error:", e);
        }

        return true;
    }

    /**
     * 判断对象是否为空，且对象的指定属性是否都为空
     * @param obj
     * @param fieldList 指定属性集合
     * @return
     * @throws Exception
     */
    public static boolean specificFieldsIsNull(Object obj, String... fieldList) {
        boolean flag = true;
        try {
            if (obj == null) {
                return true;
            }

            Map<String, Field> fieldsMap = getFieldsForMap(obj);
            for (String fieldName : fieldList) {//遍历属性
                Field field = fieldsMap.get(fieldName);
                field.setAccessible(true); // 设置属性是可以访问的(私有的也可以)

                if(Collection.class.isAssignableFrom(field.getType())) {
                    Collection val = (Collection) field.get(obj);
                    if(val != null && val.size() > 0) {
                        flag = false;
                        break;
                    }
                }
                if(Map.class.isAssignableFrom(field.getType())) {
                    Map val = (Map) field.get(obj);
                    if(val != null && val.size() > 0) {
                        flag = false;
                        break;
                    }
                }
                if(String.class.isAssignableFrom(field.getType())) {
                    String val = (String) field.get(obj);
                    if(StringUtils.isNotEmpty(val)) {
                        flag = false;
                        break;
                    }
                }
                Object val = field.get(obj);// 得到此属性的值
                if(val != null) {//只要有一个属性不为空,那么就不是所有的属性值都为空
                    flag = false;
                    break;
                }
            }
            fieldsMap.clear();
        }
        catch (Exception e) {
            logger.error("specificFieldsIsNull error:", e);
        }

        return flag;
    }

    /**
     * 获取obj对象所有的Field(包括父类)
     *
     * Field[] fields = class.getDeclaredFields()；//该方法能获取到本类的所有属性，包括private，protected和public，但不能获取到继承的父类的属性。
     * class.getFields()既能获取本类的属性也能得到父类的属性，但仅仅能获取public修饰的字段，所以采用遍历父类
     * @param obj
     * @return
     */
    public static Map<String, Field> getFieldsForMap(Object obj) {
        List<Field> resultList = new ArrayList<>();
        Field[] second;
        for (Class<?> superClass = obj.getClass(); superClass != Object.class; superClass = superClass
                .getSuperclass()) {
            second = superClass.getDeclaredFields();
            List<Field> list = Arrays.asList(second);
            resultList.addAll(list);
        }
        Map<String, Field> resultMap = new HashMap<>();
        if (resultList.size() > 0) {
            for (Field field : resultList) {
                resultMap.put(field.getName(), field);
            }
        }
        resultList.clear();
        return resultMap;
    }

    /**
     * 判断对象是否为空，且对象的所有属性都为空
     * @param obj
     * @return
     * @throws Exception
     */
    public static boolean allFieldsIsNull(Object obj) {
        boolean flag = true;
        try {
            if (obj == null) {
                return true;
            }

            List<Field> fields = getFields(obj);
            for (Field field : fields) {//遍历属性
                field.setAccessible(true); // 设置属性是可以访问的(私有的也可以)
                if(Collection.class.isAssignableFrom(field.getType())) {
                    Collection val = (Collection) field.get(obj);
                    if(val != null && val.size() > 0) {
                        flag = false;
                        break;
                    }
                }
                if(Map.class.isAssignableFrom(field.getType())) {
                    Map val = (Map) field.get(obj);
                    if(val != null && val.size() > 0) {
                        flag = false;
                        break;
                    }
                }
                if(String.class.isAssignableFrom(field.getType())) {
                    String val = (String) field.get(obj);
                    if(StringUtils.isNotEmpty(val)) {
                        flag = false;
                        break;
                    }
                }
                Object val = field.get(obj);// 得到此属性的值
                if(val != null) {//只要有一个属性不为空,那么就不是所有的属性值都为空
                    flag = false;
                    break;
                }
            }
            fields.clear();
        }
        catch (Exception e) {
            logger.error("allFieldsIsNull error:", e);
        }

        return flag;
    }

    /**
     * 获取obj对象所有的Field(包括父类)
     *
     * Field[] fields = class.getDeclaredFields()；//该方法能获取到本类的所有属性，包括private，protected和public，但不能获取到继承的父类的属性。
     * class.getFields()既能获取本类的属性也能得到父类的属性，但仅仅能获取public修饰的字段，所以采用遍历父类
     * @param obj
     * @return
     */
    public static List<Field> getFields(Object obj) {
        List<Field> result = new ArrayList<>();
        Field[] second;
        for (Class<?> superClass = obj.getClass(); superClass != Object.class; superClass = superClass
                .getSuperclass()) {
            second = superClass.getDeclaredFields();
            result.addAll(Arrays.asList(second));
        }
        return result;
    }
}
