package com.leiting.ltvip.util;


import com.google.common.hash.Hashing;
import com.leiting.ltvip.model.vo.JingyuEventNotice;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;

/**
 * 鲸域工具
 * https://open.xunjinet.com.cn/md/__03-%E7%AC%AC%E4%B8%89%E6%96%B9%E5%BA%94%E7%94%A8-callback.html
 * <AUTHOR>
 * @date 2025/5/19 14:16
 */
public class JingyuUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(JingyuUtil.class);
    
    // 追踪用日志，用于上线初期观察
    public static void info(String msg, Object... args) {
        logger.error("鲸域 " + msg, args);
    }
    
    // 计算签名
    public static String calcSign(JingyuEventNotice event) {
        // 1. 字典序排序
        String[] data = new String[5];
        data[0] = event.getAppKey();
        data[1] = event.getToken();
        data[2] = event.getNonce();
        data[3] = event.getTimestamp();
        data[4] = event.getEncodingContent();
        Arrays.sort(data);
        
        // 2. 拼接成一个字符串
        StringBuilder encryptStr = new StringBuilder();
        for (String v : data) {
            encryptStr.append(v);
        }
        
        // 3. md5加密
        MessageDigest m;
        try {
            m = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            logger.error("鲸域 计算签名异常", e);
            return "";
        }
        m.update(encryptStr.toString().getBytes());
        byte[] s = m.digest();
        StringBuilder result = new StringBuilder();
        
        for (byte b : s) {
            result.append(Integer.toHexString((0x000000FF & b) | 0xFFFFFF00).substring(6));
        }
        return result.toString();
    }
    
    // 内容解密
    public static String decrypt(String content, String aesKey) {
        if (StringUtils.isAnyBlank(content, aesKey)) {
            logger.error("鲸域 解密内容 失败 content={} aesKey={}", content, aesKey);
            return "";
        }
        
        try {
            int base = 16;
            byte[] raw = aesKey.getBytes();
            byte[] ivRaw = new byte[base];
            System.arraycopy(raw, 0, ivRaw, 0, base);
            
            SecretKeySpec keySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec iv = new IvParameterSpec(ivRaw);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, iv);
            
            // 1. 先用base64解密
            byte[] encrypted = Base64.getDecoder().decode(content);
            // 2. AES解密
            byte[] original = cipher.doFinal(encrypted);
            return new String(original);
        } catch (Exception e) {
            logger.error("鲸域 解密内容 异常 content={}", content, e);
            return "";
        }
    }
    
    // 生成消息id
    public static String genMsgId() {
        return DateUtil.getDateTimeStrShot(new Date()) + RandomStringUtils.randomNumeric(3);
    }
    
    // 生成sessionId
    public static Long genSessionId(String senderId) {
        return Hashing.murmur3_128().hashString(senderId, StandardCharsets.UTF_8).asLong();
    }
    
}
