package com.leiting.ltvip.util;

import com.google.gson.*;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GsonUtil {
    private static final Logger logger = LoggerFactory.getLogger(GsonUtil.class);
    private static String dateFormatDate = "yyyy-MM-dd";
    private static String dateFormatDateTime = "yyyy-MM-dd HH:mm:ss";
    
    // 支持下划线转驼峰
    public static Gson LOWER_CASE_WITH_UNDERSCORES = new GsonBuilder()
                                                             .registerTypeAdapter(new TypeToken<Map<String, Object>>() {
                                                             }.getType(), new DataTypeAdapter())
                                                             .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                                                             .setDateFormat(dateFormatDateTime)
                                                             .create();
    
    private static class DataTypeAdapter extends TypeAdapter<Object> {
        private final TypeAdapter<Object> delegate = new Gson().getAdapter(Object.class);

        @Override
        public Object read(JsonReader in) throws IOException {
            JsonToken token = in.peek();
            switch (token) {
                case BEGIN_ARRAY:
                    List<Object> list = new ArrayList<>();
                    in.beginArray();
                    while (in.hasNext()) {
                        list.add(read(in));
                    }
                    in.endArray();
                    return list;

                case BEGIN_OBJECT:
                    Map<String, Object> map = new LinkedTreeMap<>();
                    in.beginObject();
                    while (in.hasNext()) {
                        map.put(in.nextName(), read(in));
                    }
                    in.endObject();
                    return map;

                case STRING:
                    return in.nextString();

                case NUMBER:
                    /**
                     * 改写数字的处理逻辑，将数字值分为整型与浮点型。
                     */
                    double dbNum = in.nextDouble();

                    // 数字超过long的最大值，返回浮点类型
                    if (dbNum > Long.MAX_VALUE) {
                        return dbNum;
                    }
                    // 判断数字是否为整数值
                    long lngNum = (long) dbNum;
                    if (dbNum == lngNum) {
                        try {
                            return (int) lngNum;
                        } catch (Exception e) {
                            return lngNum;
                        }
                    } else {
                        return dbNum;
                    }

                case BOOLEAN:
                    return in.nextBoolean();

                case NULL:
                    in.nextNull();
                    return null;

                default:
                    throw new IllegalStateException();
            }
        }

        @Override
        public void write(JsonWriter out, Object value) throws IOException {
            delegate.write(out, value);
        }
    }

    public static Gson formatDateTimeGson() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>(){}.getType(), new DataTypeAdapter());
        gsonBuilder.setDateFormat(dateFormatDateTime);
        return gsonBuilder.create();
    }

    public static class DateDeserializer implements JsonDeserializer<Date> {
        public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            return new Date(json.getAsJsonPrimitive().getAsLong());
        }
    }

    public static Gson formatTimestampGson() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(Date.class, new DateDeserializer());
        return gsonBuilder.create();
    }

    public static Gson formatDateGson() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(new TypeToken<Map<String,Object>>(){}.getType(), new DataTypeAdapter());
        gsonBuilder.setDateFormat(dateFormatDate);
        return gsonBuilder.create();
    }

    public static String toJson(Object source) {
        Gson gson = formatDateTimeGson();
        return gson.toJson(source);
    }

    public static <T> T fromJson(String json, Class<T> type) {
        Gson gson = formatDateTimeGson();
        return gson.fromJson(json, type);
    }

    public static <T> List<T> fromList(String json, Class<T> type) {
        Gson gson = formatDateTimeGson();
        return gson.fromJson(json, new TypeToken<List<T>>(){}.getType());
    }

    public static <T> T fromJsonWithTimestamp(String json, Class<T> type) {
        Gson gson = formatTimestampGson();
        return gson.fromJson(json, type);
    }

    public static <T> List<T> fromListOfTimestamp(String json, Class<T> type) {
        Gson gson = formatTimestampGson();
        return gson.fromJson(json, new TypeToken<List<T>>(){}.getType());
    }

    public static <T> T fromJson(String json, Type type) {
        Gson gson = formatDateTimeGson();
        return gson.fromJson(json, type);
    }

    public static Map<String, Object> jsonToMap(String json) {
        try {
            Gson gson = formatDateTimeGson();
            Map<String, Object> map = gson.fromJson(json, new TypeToken<Map<String, Object>>() {
            }.getType());
            return map;
        } catch (Exception e) {
            logger.error("json转map失败,json:" + json, e);
        }
        return new HashMap<>();
    }

    /**
     * json数组转list，附带泛型转换
     * @param obj
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> parseObj2List(Object obj, Class<T> clazz) {
        String json = GsonUtil.toJson(obj);
        Gson gson = formatDateTimeGson();
        Type type = new ParameterizedTypeImpl<>(clazz);
        return gson.fromJson(json, type);
    }

    /**
     * json数组转list，附带泛型转换
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> parseString2List(String json, Class<T> clazz) {
        Gson gson = formatDateTimeGson();
        Type type = new ParameterizedTypeImpl<>(clazz);
        return gson.fromJson(json, type);
    }

    /**
     * 参数类型自动转化器
     * @param <T>
     */
    public static class ParameterizedTypeImpl<T> implements ParameterizedType {
        Class<T> clazz;

        public ParameterizedTypeImpl(Class<T> clz) {
            clazz = clz;
        }

        @Override
        public Type[] getActualTypeArguments() {
            return new Type[]{clazz};
        }

        @Override
        public Type getRawType() {
            return List.class;
        }

        @Override
        public Type getOwnerType() {
            return null;
        }
    }
}
