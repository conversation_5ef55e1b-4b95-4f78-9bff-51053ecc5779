package com.leiting.ltvip.util;

/**
 * 方法参数工具类
 * <AUTHOR>
 * @date 2025/1/27
 */
public class MethodParamUtil {

    /**
     * 根据类型查找方法参数
     * @param args 方法参数数组
     * @param targetType 目标类型
     * @param <T> 泛型类型
     * @return 找到的参数，如果没找到返回null
     */
    @SuppressWarnings("unchecked")
    public static <T> T findParamByType(Object[] args, Class<T> targetType) {
        if (args == null || args.length == 0 || targetType == null) {
            return null;
        }
        
        for (Object arg : args) {
            if (arg != null && targetType.isInstance(arg)) {
                return (T) arg;
            }
        }
        
        return null;
    }

    /**
     * 检查方法参数中是否包含指定类型
     * @param args 方法参数数组
     * @param targetType 目标类型
     * @return 是否包含指定类型的参数
     */
    public static boolean hasParamOfType(Object[] args, Class<?> targetType) {
        return findParamByType(args, targetType) != null;
    }

    /**
     * 获取指定类型参数的索引位置
     * @param args 方法参数数组
     * @param targetType 目标类型
     * @return 参数索引，如果没找到返回-1
     */
    public static int getParamIndexByType(Object[] args, Class<?> targetType) {
        if (args == null || args.length == 0 || targetType == null) {
            return -1;
        }
        
        for (int i = 0; i < args.length; i++) {
            if (args[i] != null && targetType.isInstance(args[i])) {
                return i;
            }
        }
        
        return -1;
    }
}
