package com.leiting.ltvip.controller.api;


import com.leiting.config.api.anotation.LtValue;
import com.leiting.framework.bean.JsonResult;
import com.leiting.ltvip.constant.CommonConst.QiYu;
import com.leiting.ltvip.qiyu.common.ChatLogUtil;
import com.leiting.ltvip.qiyu.model.QiyuChatQuestion;
import com.leiting.ltvip.qiyu.common.QiYuCheckSum;
import com.leiting.ltvip.qiyu.service.QiyuModelChatService;
import com.leiting.ltvip.service.bailian.BailianService;
import com.leiting.ltvip.util.GsonUtil;
import com.leiting.ltvip.util.QiyuSSEUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.concurrent.*;

/**
 * 七鱼API接口
 * <a href="https://b.163.com/knowledge/public/WXjbs9n3GC/knowdetail?docId=X8GeGW2MVs&pid=162307">通用说明</a>
 * <a href="https://b.163.com/knowledge/public/WXjbs9n3GC/knowdetail?docId=WXkNWaWoHQ&pid=0">数据加密策略</a>
 *
 * <AUTHOR>
 * @date 2025/3/25 14:31
 */
@RestController
@RequestMapping("api/qiyu")
public class QiyuController {
    
    private static final Logger logger = LoggerFactory.getLogger(QiyuController.class);
    
    @Autowired
    private QiyuModelChatService qiyuModelChatService;
    
    @LtValue(value = QiYu.QI_YU_API_APP_ID_CONFIG_KEY, defaultValue = QiYu.QI_YU_APP_KEY)
    private String qiyuAppKey; // 七鱼管理后台配置的appKey
    
    @LtValue("qiyu.merchant.appSecret")
    private String qiyuMerchantAppSecret; // 七鱼管理后台配置的CRM信息对接中的appSecret
    
    private static final int VERIFY_TIMEOUT_SECOND = 5 * 60; // 有效期(秒)
    private static final long SSE_TIMEOUT_MILLI = 60_000L; // SSE超时时间(毫秒)
    
    private static final ExecutorService executorService = new ThreadPoolExecutor(
            20,         // 核心线程数（CPU×2+1）
            50,         // 最大线程数（根据QPS峰值调整）
            60L,        // 空闲线程存活时间
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000)  // 任务队列缓冲
    );
    @Autowired
    private BailianService bailianService;
    
    // 鉴权接口，通过请求方式区分，七鱼管理后台要求是同一个URL链接
    @GetMapping("chat/{workspace}/{bailianAppId}")
    public JsonResult.JsonData verify(@PathVariable("workspace") String workspace, @PathVariable("bailianAppId") String bailianAppId,
                                      @RequestParam(value = "time") Long time, @RequestParam(value = "checksum") String checksum) {
        ChatLogUtil.info("收到验证 workspace={} appId={} time={} checksum={}", workspace, bailianAppId, time, checksum);
        
        // 验证时间是否有效
        if (isTimeExpired(time)) {
            logger.error("七鱼问答 请求已过期 workspace={} appId={} time={} checksum={}", workspace, bailianAppId, time, checksum);
            return JsonResult.success(QiYu.CODE_KEY, QiYu.FAIL).message("请求已过期");
        }
        // 验证工作空间是否配置百炼apiKey
        if (isInvalidWorkspace(workspace)) {
            logger.error("七鱼问答 未配置工作空间密钥 workspace={} appId={} time={} checksum={}", workspace, bailianAppId, time, checksum);
            return JsonResult.success(QiYu.CODE_KEY, QiYu.FAIL).message("未配置对应工作空间apiKey");
        }
        
        ChatLogUtil.info("返回成功 workspace={} appId={} checksum={} return={}", workspace, bailianAppId, checksum,
                JsonResult.success(QiYu.CODE_KEY, QiYu.SUCCESS).message("success").data(checksum));
        return JsonResult.success(QiYu.CODE_KEY, QiYu.SUCCESS).message("success").data(checksum);
    }
    
    // 大模型问答-自动
    @PostMapping(value = "chat/{workspace}/{bailianAppId}", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    public Object chat(@PathVariable("workspace") String workspace, @PathVariable("bailianAppId") String bailianAppId,
                       @RequestParam(value = "time") Long time, @RequestParam(value = "checksum") String checksum, @RequestBody String rawJson) {
        // 由于请求body要参与签名运算，故直接接收原始json字符串，再转换成对象，避免格式变化
        QiyuChatQuestion chatQuestion = GsonUtil.fromJson(rawJson, QiyuChatQuestion.class);
        String calcChecksum = QiYuCheckSum.encode(qiyuMerchantAppSecret, rawJson, String.valueOf(time));
        ChatLogUtil.info("收到请求 workspace={} appId={} time={} checksum={} calcChecksum={} rawJson={} chatQuestion={}",
                workspace, bailianAppId, time, checksum, calcChecksum, rawJson, chatQuestion);
        
        boolean stream = Boolean.TRUE.equals(chatQuestion.getStream()); // 是否流式输出
        // 验证时间是否有效
        if (isTimeExpired(time)) {
            logger.error("七鱼问答 请求已过期 workspace={} appId={} time={} checksum={} calcChecksum={} rawJson={} chatQuestion={}",
                    workspace, bailianAppId, time, checksum, calcChecksum, rawJson, chatQuestion);
            return stream ? error("请求已过期") : JsonResult.fail(QiYu.CODE_KEY, QiYu.FAIL).message("请求已过期");
        }
        // TODO 待校验 checksum（担心对线上已有接口有影响，暂未校验）
        
        
        if (stream) { // 流式输出
            SseEmitter emitter = new SseEmitter(SSE_TIMEOUT_MILLI);
            executorService.execute(() -> qiyuModelChatService.chatStream(emitter, workspace, bailianAppId, chatQuestion));
            return emitter;
        } else { // 回调输出
            executorService.execute(() -> qiyuModelChatService.chatCallback(workspace, bailianAppId, chatQuestion));
            return JsonResult.success(QiYu.CODE_KEY, QiYu.SUCCESS).message("success").data(checksum);
        }
    }
    
    // 时间已过期，time单位:秒
    private boolean isTimeExpired(Long time) {
        return Math.abs(System.currentTimeMillis() / 1000 - time) > VERIFY_TIMEOUT_SECOND;
    }
    
    // 验证是否配置了工作空间密钥。true-无效，false-有效
    private boolean isInvalidWorkspace(String workspace) {
        return !bailianService.isValidWorkspace(workspace);
    }
    
    // checksum匹配通过
    private boolean matchChecksum(String checksum, String rawJson, Long time) {
        if (StringUtils.isBlank(checksum)) {
            return false;
        }
        String calcChecksum = QiYuCheckSum.encode(qiyuMerchantAppSecret, rawJson, String.valueOf(time));
        return StringUtils.equals(checksum, calcChecksum);
    }
    
    // 通用错误方法
    private SseEmitter error(String message) {
        SseEmitter emitter = new SseEmitter();
        QiyuSSEUtil.sendError(emitter, message);
        QiyuSSEUtil.close(emitter);
        return emitter;
    }
    
}
