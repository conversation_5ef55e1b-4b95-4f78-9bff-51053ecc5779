package com.leiting.ltvip.controller.common;

import com.google.common.collect.Maps;
import com.leiting.config.api.anotation.LtValue;
import com.leiting.framework.util.WebUtil;
import com.leiting.framework.web.BaseController;
import com.leiting.guise.GsonUtil;
import com.leiting.ltvip.annotation.ValidateDiaodu;
import com.leiting.ltvip.common.ScheduleTask;
import com.leiting.ltvip.constant.CommonConst;
import com.leiting.ltvip.constant.RedisConst;
import com.leiting.ltvip.service.*;
import com.leiting.ltvip.service.api.SupportApiService;
import com.leiting.ltvip.service.common.CreditExpireService;
import com.leiting.ltvip.service.common.GiftService;
import com.leiting.ltvip.service.schedule.GameTalkingRetryService;
import com.leiting.ltvip.service.schedule.SynVipUserInfoService;
import com.leiting.ltvip.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.ServletContext;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@ApiIgnore
@RestController
@RequestMapping(value = "schedule")
@ValidateDiaodu // 校验调度地址IP
public class ScheduledController extends BaseController {
    @Autowired
    SynVipUserInfoService synVipUserInfoService;
    @Autowired
    GameTalkingRetryService gameTalkingRetryService;
    @Autowired
    RedisLockerStringUtil redisLocker;
    @Autowired
    private BirthGiftLogService giftLogService;

    @Autowired
    private SupportApiService supportApiService;

    @Autowired
    private VipMessageNotifyService vipMessageNotifyService;

    @Autowired
    private ToothCommonBindService toothCommonBindService;

    @Autowired
    private CreditExpireService creditExpireService;

    @Autowired
    private UserCreditActiveRecordService userCreditActiveRecordService;

    @Autowired
    private CreditGiftDrawLogService creditGiftDrawLogService;

    @Autowired
    private AuthLoginService authLoginService;

    @Autowired
    private CreditAwardConfigService creditAwardConfigService;

    @Autowired
    private NewsConfigService newsConfigService;

    @Autowired
    private SuperiorActivityService superiorActivityService;

    @Autowired
    private GiftService giftService;
    @Autowired
    private VipUserInfoService vipUserInfoService;

    @Value("${prophet.url}")
    private String prophetUrl;

    @LtValue("prophet.push.data")
    private String pushData;

    //0 0 */3 * * ?
    @RequestMapping("/vipInfoTask")
    @ResponseBody
    public Object vipInfoTask() {
        String lock = RedisConst.SYN_CHARGE_INFO_TASK_LOCK;
        if (redisLocker.tryLockNotRetry(lock, 30, TimeUnit.MINUTES)) {
            Thread syn = new Thread(() -> synVipUserInfoService.synUserInfoForSchedule(lock));
            syn.start();
        }

        return "success";
    }

    @RequestMapping("/gameTalkingRetry")
    @ResponseBody
    public Object gameTalkingRetry() {
        String lock = RedisConst.GAME_TALKING_RETRY_LOCK;
        if (redisLocker.tryLockNotRetry(lock, 20, TimeUnit.MINUTES)) {
            Thread syn = new Thread(() -> gameTalkingRetryService.retryToNotify(lock));
            syn.start();
        }

        return "success";
    }

    //0 */10 * * * ? 每十分钟发送一次生日礼包
    @RequestMapping("/birthdayGiftSend")
    @ResponseBody
    public Object birthdayGiftSend() {
        try {
            giftLogService.sendVipBirthGift();
        } catch (Exception commonException) {
            logger.error("birthdayGiftSendTaskException", commonException);
        }

        return "success";
    }

    // 用于 自助查询恢复-仙尊名单推送（一念逍遥）
    @RequestMapping("/pushSelfHelpQueryData")
    @ResponseBody
    public Object pushSelfHelpQueryData(@RequestParam("gameCode") String gameCode, @RequestParam("sourceMark") String sourceMark) {
        try {
            List<String> xianDataList = vipUserInfoService.getSidByGame(gameCode);
            Map<String, Object> paramMap = Maps.newHashMap();
            Long timestamp = new Date().getTime();
            String sign = Md5Util.encryptMsg(gameCode + timestamp + pushData);
            paramMap.put("gameCode", gameCode);
            paramMap.put("timestamp", timestamp);
            paramMap.put("sign", sign);
            paramMap.put("sourceMark", sourceMark);
            paramMap.put("dataList", xianDataList);
            HttpUtil.postJson(null, prophetUrl + CommonConst.PUSH_DATA, GsonUtil.toJson(paramMap));
        } catch (Exception e) {
            logger.error("getSidByGameException", e);
        }
        return "success";
    }

    //0 */20 * * * ? 每二十分钟进行会员信息变更通知客服系统重试
    @RequestMapping("/notifySupportRetry")
    @ResponseBody
    public Object notifySupportRetry() {
        String lock = RedisConst.VIP_INFO_CHANGE_NOTIFY_RETRY_LOCK;
        if (redisLocker.tryLockNotRetry(lock, 50, TimeUnit.MINUTES)) {
            Thread syn = new Thread(() -> {
                try {
                    supportApiService.notifySupportRetry();
                } catch (Exception exception) {
                    logger.error("notifySupportRetryException", exception);
                } finally {
                    redisLocker.releaseLock(lock);
                }
            });
            syn.start();
        }
        return "success";
    }

    //0 30 */2 * * ? 每天0点30分开始隔两个小时执行
    //处理数据中心推送过来的玩家活跃度数据
    @RequestMapping("/collectActiveVipForAddCredit")
    @ResponseBody
    public Object collectActiveVipForAddCredit() {
        String statisticDate;
        try {
            Date now = new Date();
            Date yesterday = DateUtil.addTimeByDayOfDate(now, -1);
            statisticDate = DateUtil.getDateStr(yesterday);
        } catch (Exception exception) {
            logger.error("collectActiveVipForAddCredit日期转换错误");
            return "error";
        }

        String lock = RedisConst.getActiveAddCreditLockKey(statisticDate);
        if (redisLocker.tryLockNotRetry(lock, 2, TimeUnit.HOURS)) {
            Thread syn = new Thread(() -> {
                try {
                    userCreditActiveRecordService.addCreditForActive(statisticDate);
                } catch (Exception exception) {
                    logger.error("collectActiveVipForAddCreditException:", exception);
                } finally {
                    redisLocker.releaseLock(lock);
                }
            });
            syn.start();
        }
        return "success";
    }

    //0 0 */5 * * ?
    @RequestMapping("/cleanTempFile")
    @ResponseBody
    public Object cleanTempFile() {
        Date now = new Date();
        ServletContext servletContext = WebUtil.getRequest().getServletContext();

        for (int diffDays = 3; diffDays < 15; diffDays++) {
            Date targetDate = null;
            try {
                targetDate = DateUtil.addTimeByDayOfDate(now, -diffDays);
                String dateStr = DateUtil.getDateStr(targetDate);
                String cleanFolder = FileUtil.getCleanFolderPath(dateStr);
                cleanFolder = servletContext.getRealPath(cleanFolder);

                FileUtil.deleteDirectory(cleanFolder);
            } catch (ParseException e) {
                logger.error("cleanTempFile error, targetDate:" + targetDate, e);
            }
        }
        return "success";
    }

    // 0 */20 * * * ? 每二十分钟进行新晋会员短信通知
    @RequestMapping("/newVipSendMessage")
    @ResponseBody
    public Object newVipSendMessage() {
        String lock = RedisConst.NEW_VIP_MESSAGE_LOCK;
        if (redisLocker.tryLockNotRetry(lock, 20, TimeUnit.MINUTES)) {
            Thread syn = new Thread(() -> {
                try {
                    vipMessageNotifyService.notifyMessage();
                } catch (Exception exception) {
                    logger.error("newVipSendMessageException:", exception);
                } finally {
                    redisLocker.releaseLock(lock);
                }
            });
            syn.start();
        }
        return "success";
    }

    // 0 */20 * * * ? 每二十分钟进行新晋会员短信通知
    @RequestMapping("/tryNotifyToothCustomTag")
    @ResponseBody
    public Object tryNotifyToothCustomTag() {
        String lock = RedisConst.TOOTH_CUSTOM_TAG_TASK_LOCK;
        if (redisLocker.tryLockNotRetry(lock, 40, TimeUnit.MINUTES)) {
            Thread syn = new Thread(() -> {
                try {
                    toothCommonBindService.tryNotifyTooth();
                } catch (Exception exception) {
                    logger.error("tryNotifyToothCustomTagException:", exception);
                } finally {
                    redisLocker.releaseLock(lock);
                }
            });
            syn.start();
        }
        return "success";
    }

    /**
     * 扣除会员上个月过期积分
     *
     * @return
     */
    @RequestMapping("/reduceUserExpireCredit")
    @ResponseBody
    public Object reduceUserExpireCredit() {
        String lock = RedisConst.REDUCE_EXPIRE_CREDIT_TASK;
        if (redisLocker.tryLockNotRetry(lock, 2, TimeUnit.HOURS)) {
            Thread syn = new Thread(() -> {
                try {
                    creditExpireService.reduceExpireCredit();
                } catch (Exception exception) {
                    logger.error("reduceUserExpireCreditException:", exception);
                } finally {
                    redisLocker.releaseLock(lock);
                }
            });
            syn.start();
        }
        return "success";
    }

    /**
     * 统计会员本月过期积分
     *
     * @return
     */
    @RequestMapping("/statisticUserExpireCredit")
    @ResponseBody
    public Object statisticUserExpireCredit() {
        String lock = RedisConst.STATISTIC_EXPIRE_CREDIT_TASK;
        if (redisLocker.tryLockNotRetry(lock, 2, TimeUnit.HOURS)) {
            Thread syn = new Thread(() -> {
                try {
                    creditExpireService.statisticExpireCreditThisMonth();
                } catch (Exception exception) {
                    logger.error("statisticUserExpireCreditException:", exception);
                } finally {
                    redisLocker.releaseLock(lock);
                }
            });
            syn.start();
        }
        return "success";
    }

    /**
     * 补发发送失败的礼包
     *
     * @return
     */
    @RequestMapping("/resendFailGift")
    @ResponseBody
    public Object resendFailGift() {
        runTask(RedisConst.RESEND_FAIL_GIFT_TASK,
                () -> creditGiftDrawLogService.resendFailGift());
        return "success";
    }

    /**
     * 补发超时未领取的礼包
     *
     * @return
     */
    @RequestMapping("/sendUnDrawGift")
    @ResponseBody
    public Object sendUnDrawGift() {
        runTask(RedisConst.SEND_UN_DRAW_GIFT_TASK,
                () -> creditGiftDrawLogService.sendUnDrawGift());
        return "success";
    }

    /**
     * 超过7天未领取的礼包自动失效
     *
     * @return
     */
    @RequestMapping("/disableUnDrawGift")
    @ResponseBody
    public Object disableUnDrawGift() {
        runTask(RedisConst.DISABLE_UN_DRAW_GIFT_TASK,
                () -> creditGiftDrawLogService.disableUnDrawGift());
        return "success";
    }

    //执行定时任务
    public void runTask(String lock, ScheduleTask task) {
        if (redisLocker.tryLockNotRetry(lock, 3, TimeUnit.HOURS)) {
            Thread syn = new Thread(() -> {
                try {
                    task.run();
                } catch (Exception exception) {
                    logger.error("runTaskException:", exception);
                } finally {
                    redisLocker.releaseLock(lock);
                }
            });
            syn.start();
        }
    }

    /**
     * 定时任务检测授权码是否过期
     *
     * @return
     */
    @RequestMapping("auth/check_will_expire")
    @ResponseBody
    public Object authCheckWillExpire() {
        authLoginService.checkWillExpireList();
        return "success";
    }

    /**
     * 自动更新配置状态
     *
     * @return
     */
    @RequestMapping("/autoUpdateConfigStatus")
    @ResponseBody
    public Object autoUpdateConfigStatus() {
        Thread task = new Thread(
                () -> {
                    creditAwardConfigService.autoUpdateStatus();
                    newsConfigService.removalConfig();
                    superiorActivityService.removalConfig();
                }
        );
        task.start();
        return "success";
    }

    /**
     * 发送微信订阅消息
     *
     * @return
     */
    @RequestMapping("/sendWxSubscribeMsg")
    @ResponseBody
    public Object sendWxSubscribeMsg() {
        newsConfigService.sendWxSubscribeMsg();
        giftService.sendWxSubscribeMsg();
        return "success";
    }

    // 便于验证调度IP判断是否正常
    @RequestMapping("check_diaodu")
    @ValidateDiaodu(testEnvSkipValidate = false) // 校验调度地址IP
    public String checkDiaodu() {
        return "success";
    }

}
