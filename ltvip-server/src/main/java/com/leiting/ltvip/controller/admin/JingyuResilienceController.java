package com.leiting.ltvip.controller.admin;

import com.leiting.ltvip.service.jingyu.JingyuResilienceMonitorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 鲸域Resilience4j管理接口
 * 提供监控和管理功能
 * <AUTHOR>
 * @date 2025/1/27
 */
@RestController
@RequestMapping("/admin/jingyu/resilience")
public class JingyuResilienceController {
    
    private static final Logger logger = LoggerFactory.getLogger(JingyuResilienceController.class);
    
    @Autowired
    private JingyuResilienceMonitorService monitorService;
    
    /**
     * 获取所有Resilience4j状态
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", monitorService.getAllStatus());
            result.put("message", "获取状态成功");
            return result;
        } catch (Exception e) {
            logger.error("获取鲸域Resilience4j状态异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取状态失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 获取发送接口限流器状态
     */
    @GetMapping("/send/ratelimiter")
    public Map<String, Object> getSendRateLimiterStatus() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", monitorService.getSendRateLimiterStatus());
            result.put("message", "获取发送接口限流器状态成功");
            return result;
        } catch (Exception e) {
            logger.error("获取发送接口限流器状态异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取状态失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 获取发送接口熔断器状态
     */
    @GetMapping("/send/circuitbreaker")
    public Map<String, Object> getSendCircuitBreakerStatus() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", monitorService.getSendCircuitBreakerStatus());
            result.put("message", "获取发送接口熔断器状态成功");
            return result;
        } catch (Exception e) {
            logger.error("获取发送接口熔断器状态异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取状态失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 获取认证接口限流器状态
     */
    @GetMapping("/auth/ratelimiter")
    public Map<String, Object> getAuthRateLimiterStatus() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", monitorService.getAuthRateLimiterStatus());
            result.put("message", "获取认证接口限流器状态成功");
            return result;
        } catch (Exception e) {
            logger.error("获取认证接口限流器状态异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取状态失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 获取认证接口熔断器状态
     */
    @GetMapping("/auth/circuitbreaker")
    public Map<String, Object> getAuthCircuitBreakerStatus() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", monitorService.getAuthCircuitBreakerStatus());
            result.put("message", "获取认证接口熔断器状态成功");
            return result;
        } catch (Exception e) {
            logger.error("获取认证接口熔断器状态异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取状态失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 重置熔断器状态（紧急情况下使用）
     */
    @PostMapping("/reset")
    public Map<String, Object> resetCircuitBreakers() {
        try {
            monitorService.resetCircuitBreakers();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "熔断器重置成功");
            logger.warn("管理员手动重置鲸域熔断器状态");
            return result;
        } catch (Exception e) {
            logger.error("重置鲸域熔断器状态异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "重置失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 获取Resilience4j配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            // 发送接口配置
            Map<String, Object> sendConfig = new HashMap<>();
            sendConfig.put("rateLimiter", Map.of(
                "limitForPeriod", 10,
                "limitRefreshPeriod", "1s",
                "timeoutDuration", "3s"
            ));
            sendConfig.put("circuitBreaker", Map.of(
                "failureRateThreshold", 50,
                "slowCallRateThreshold", 80,
                "slowCallDurationThreshold", "5s",
                "waitDurationInOpenState", "30s",
                "slidingWindowSize", 20,
                "minimumNumberOfCalls", 10
            ));
            config.put("send", sendConfig);
            
            // 认证接口配置
            Map<String, Object> authConfig = new HashMap<>();
            authConfig.put("rateLimiter", Map.of(
                "limitForPeriod", 5,
                "limitRefreshPeriod", "1s",
                "timeoutDuration", "2s"
            ));
            authConfig.put("circuitBreaker", Map.of(
                "failureRateThreshold", 60,
                "slowCallRateThreshold", 70,
                "slowCallDurationThreshold", "10s",
                "waitDurationInOpenState", "60s",
                "slidingWindowSize", 15,
                "minimumNumberOfCalls", 5
            ));
            config.put("auth", authConfig);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", config);
            result.put("message", "获取配置成功");
            return result;
        } catch (Exception e) {
            logger.error("获取鲸域Resilience4j配置异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取配置失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        try {
            Map<String, Object> status = monitorService.getAllStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", Map.of(
                "status", "UP",
                "resilience4j", status
            ));
            result.put("message", "服务健康");
            return result;
        } catch (Exception e) {
            logger.error("鲸域Resilience4j健康检查异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("data", Map.of("status", "DOWN"));
            result.put("message", "服务异常: " + e.getMessage());
            return result;
        }
    }
}
