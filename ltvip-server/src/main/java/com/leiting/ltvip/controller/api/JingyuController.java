package com.leiting.ltvip.controller.api;


import com.leiting.config.api.anotation.LtValue;
import com.leiting.ltvip.model.vo.JingyuEventNotice;
import com.leiting.ltvip.service.jingyu.handler.EventHandlerHolder;
import com.leiting.ltvip.util.JingyuUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 鲸域回调接口
 * https://open.xunjinet.com.cn/md/__03-%E7%AC%AC%E4%B8%89%E6%96%B9%E5%BA%94%E7%94%A8-callback.html
 * <AUTHOR>
 * @date 2025/5/19 14:06
 */
@RestController
@RequestMapping("api/jingyu")
public class JingyuController {
    
    private static final Logger logger = LoggerFactory.getLogger(JingyuController.class);
    
    @LtValue("jingyu.encoding.aesKey")
    private String encodingAesKey; // 鲸域内容加密aesKey
    
    private static final ExecutorService executorService = new ThreadPoolExecutor(
            20,         // 核心线程数（CPU×2+1）
            50,         // 最大线程数（根据QPS峰值调整）
            60L,        // 空闲线程存活时间
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000)  // 任务队列缓冲
    );
    
    // 事件通知
    @RequestMapping("callback")
    public String callback(@RequestBody JingyuEventNotice event) {
        JingyuUtil.info("收到通知 event={}", event);
        
        // 校验签名
        String calcSign = JingyuUtil.calcSign(event);
        if (!StringUtils.equals(event.getSignature(), calcSign)) {
            logger.error("鲸域 签名校验失败 requestSign={} calcSign={} event={}", event.getSignature(), calcSign, event);
            return "fail";
        }
        
        String content = JingyuUtil.decrypt(event.getEncodingContent(), encodingAesKey);
        JingyuUtil.info("内容解密 content={}", content);
        // 异步处理
        executorService.execute(() -> EventHandlerHolder.handle(content));
        return "success";
    }
    
}
