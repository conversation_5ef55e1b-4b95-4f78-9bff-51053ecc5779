package com.leiting.ltvip.resilience;

import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 基于Redis的分布式熔断器
 * <AUTHOR>
 * @date 2025/1/27
 */
@Component
public class DistributedCircuitBreaker {
    
    private static final Logger logger = LoggerFactory.getLogger(DistributedCircuitBreaker.class);
    
    @Autowired
    private RedissonClient redissonClient;
    
    // 熔断器状态
    public enum State {
        CLOSED,    // 关闭状态（正常）
        OPEN,      // 打开状态（熔断）
        HALF_OPEN  // 半开状态（测试）
    }
    
    /**
     * 检查是否允许执行
     * @param breakerName 熔断器名称
     * @param failureRateThreshold 失败率阈值
     * @param minimumNumberOfCalls 最小调用次数
     * @param waitDurationInOpenState 熔断器打开状态持续时间
     * @return 是否允许执行
     */
    public boolean isCallPermitted(String breakerName, int failureRateThreshold, 
                                  int minimumNumberOfCalls, Duration waitDurationInOpenState) {
        try {
            State currentState = getState(breakerName);
            
            switch (currentState) {
                case CLOSED:
                    return true;
                    
                case OPEN:
                    // 检查是否可以转换到半开状态
                    if (shouldTransitionToHalfOpen(breakerName, waitDurationInOpenState)) {
                        setState(breakerName, State.HALF_OPEN);
                        logger.info("分布式熔断器 {} 从OPEN转换到HALF_OPEN", breakerName);
                        return true;
                    }
                    logger.debug("分布式熔断器 {} 处于OPEN状态，拒绝调用", breakerName);
                    return false;
                    
                case HALF_OPEN:
                    return true;
                    
                default:
                    return true;
            }
            
        } catch (Exception e) {
            logger.error("分布式熔断器 {} 检查异常", breakerName, e);
            // 异常情况下允许通过，避免影响业务
            return true;
        }
    }
    
    /**
     * 记录成功调用
     * @param breakerName 熔断器名称
     */
    public void recordSuccess(String breakerName) {
        try {
            State currentState = getState(breakerName);
            
            // 增加成功计数
            incrementSuccessCount(breakerName);
            
            // 如果是半开状态，可能需要转换到关闭状态
            if (currentState == State.HALF_OPEN) {
                setState(breakerName, State.CLOSED);
                resetCounts(breakerName);
                logger.info("分布式熔断器 {} 从HALF_OPEN转换到CLOSED", breakerName);
            }
            
        } catch (Exception e) {
            logger.error("分布式熔断器 {} 记录成功异常", breakerName, e);
        }
    }
    
    /**
     * 记录失败调用
     * @param breakerName 熔断器名称
     * @param failureRateThreshold 失败率阈值
     * @param minimumNumberOfCalls 最小调用次数
     */
    public void recordFailure(String breakerName, int failureRateThreshold, int minimumNumberOfCalls) {
        try {
            // 增加失败计数
            incrementFailureCount(breakerName);
            
            // 检查是否需要打开熔断器
            if (shouldOpenCircuitBreaker(breakerName, failureRateThreshold, minimumNumberOfCalls)) {
                setState(breakerName, State.OPEN);
                setOpenTime(breakerName, System.currentTimeMillis());
                logger.warn("分布式熔断器 {} 转换到OPEN状态", breakerName);
            }
            
        } catch (Exception e) {
            logger.error("分布式熔断器 {} 记录失败异常", breakerName, e);
        }
    }
    
    /**
     * 获取熔断器状态
     */
    public CircuitBreakerStatus getStatus(String breakerName) {
        try {
            State state = getState(breakerName);
            long successCount = getSuccessCount(breakerName);
            long failureCount = getFailureCount(breakerName);
            long totalCount = successCount + failureCount;
            double failureRate = totalCount > 0 ? (double) failureCount / totalCount * 100 : 0;
            
            return new CircuitBreakerStatus(breakerName, state, successCount, failureCount, failureRate);
            
        } catch (Exception e) {
            logger.error("获取分布式熔断器 {} 状态异常", breakerName, e);
            return new CircuitBreakerStatus(breakerName, State.CLOSED, 0, 0, 0);
        }
    }
    
    /**
     * 重置熔断器
     */
    public void reset(String breakerName) {
        try {
            setState(breakerName, State.CLOSED);
            resetCounts(breakerName);
            logger.info("重置分布式熔断器: {}", breakerName);
        } catch (Exception e) {
            logger.error("重置分布式熔断器 {} 异常", breakerName, e);
        }
    }
    
    // 私有方法
    private State getState(String breakerName) {
        RBucket<String> bucket = redissonClient.getBucket("jingyu:circuitbreaker:" + breakerName + ":state");
        String stateStr = bucket.get();
        return stateStr != null ? State.valueOf(stateStr) : State.CLOSED;
    }
    
    private void setState(String breakerName, State state) {
        RBucket<String> bucket = redissonClient.getBucket("jingyu:circuitbreaker:" + breakerName + ":state");
        bucket.set(state.name(), 300, TimeUnit.SECONDS); // 5分钟过期
    }
    
    private boolean shouldTransitionToHalfOpen(String breakerName, Duration waitDuration) {
        RBucket<Long> bucket = redissonClient.getBucket("jingyu:circuitbreaker:" + breakerName + ":openTime");
        Long openTime = bucket.get();
        return openTime != null && (System.currentTimeMillis() - openTime) >= waitDuration.toMillis();
    }
    
    private void setOpenTime(String breakerName, long time) {
        RBucket<Long> bucket = redissonClient.getBucket("jingyu:circuitbreaker:" + breakerName + ":openTime");
        bucket.set(time, 300, TimeUnit.SECONDS);
    }
    
    private void incrementSuccessCount(String breakerName) {
        RAtomicLong counter = redissonClient.getAtomicLong("jingyu:circuitbreaker:" + breakerName + ":success");
        counter.incrementAndGet();
        counter.expire(300, TimeUnit.SECONDS);
    }
    
    private void incrementFailureCount(String breakerName) {
        RAtomicLong counter = redissonClient.getAtomicLong("jingyu:circuitbreaker:" + breakerName + ":failure");
        counter.incrementAndGet();
        counter.expire(300, TimeUnit.SECONDS);
    }
    
    private long getSuccessCount(String breakerName) {
        RAtomicLong counter = redissonClient.getAtomicLong("jingyu:circuitbreaker:" + breakerName + ":success");
        return counter.get();
    }
    
    private long getFailureCount(String breakerName) {
        RAtomicLong counter = redissonClient.getAtomicLong("jingyu:circuitbreaker:" + breakerName + ":failure");
        return counter.get();
    }
    
    private void resetCounts(String breakerName) {
        redissonClient.getAtomicLong("jingyu:circuitbreaker:" + breakerName + ":success").delete();
        redissonClient.getAtomicLong("jingyu:circuitbreaker:" + breakerName + ":failure").delete();
    }
    
    private boolean shouldOpenCircuitBreaker(String breakerName, int failureRateThreshold, int minimumNumberOfCalls) {
        long successCount = getSuccessCount(breakerName);
        long failureCount = getFailureCount(breakerName);
        long totalCount = successCount + failureCount;
        
        if (totalCount < minimumNumberOfCalls) {
            return false;
        }
        
        double failureRate = (double) failureCount / totalCount * 100;
        return failureRate >= failureRateThreshold;
    }
    
    /**
     * 熔断器状态信息
     */
    public static class CircuitBreakerStatus {
        private final String name;
        private final State state;
        private final long successCount;
        private final long failureCount;
        private final double failureRate;
        
        public CircuitBreakerStatus(String name, State state, long successCount, long failureCount, double failureRate) {
            this.name = name;
            this.state = state;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.failureRate = failureRate;
        }
        
        public String getName() {
            return name;
        }
        
        public State getState() {
            return state;
        }
        
        public long getSuccessCount() {
            return successCount;
        }
        
        public long getFailureCount() {
            return failureCount;
        }
        
        public double getFailureRate() {
            return failureRate;
        }
    }
}
