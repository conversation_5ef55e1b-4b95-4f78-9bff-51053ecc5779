package com.leiting.ltvip.resilience;

import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 基于Redis的分布式限流器
 * <AUTHOR>
 * @date 2025/1/27
 */
@Component
public class DistributedRateLimiter {
    
    private static final Logger logger = LoggerFactory.getLogger(DistributedRateLimiter.class);
    
    @Autowired
    private RedissonClient redissonClient;
    
    /**
     * 尝试获取许可
     * @param limiterName 限流器名称
     * @param permits 需要的许可数
     * @param limitForPeriod 每个周期允许的请求数
     * @param limitRefreshPeriod 刷新周期（秒）
     * @param timeoutDuration 等待超时时间
     * @return 是否获取成功
     */
    public boolean tryAcquire(String limiterName, int permits, long limitForPeriod, 
                             long limitRefreshPeriod, Duration timeoutDuration) {
        try {
            String key = "jingyu:ratelimiter:" + limiterName;
            RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
            
            // 设置限流器配置（如果还未设置）
            if (!rateLimiter.isExists()) {
                rateLimiter.trySetRate(RateType.OVERALL, limitForPeriod, limitRefreshPeriod, RateIntervalUnit.SECONDS);
                logger.info("初始化分布式限流器: {} 限制: {}/{}秒", limiterName, limitForPeriod, limitRefreshPeriod);
            }
            
            // 尝试获取许可
            boolean acquired = rateLimiter.tryAcquire(permits, timeoutDuration.toMillis(), TimeUnit.MILLISECONDS);
            
            if (acquired) {
                logger.debug("分布式限流器 {} 获取许可成功", limiterName);
            } else {
                logger.warn("分布式限流器 {} 获取许可失败，触发限流", limiterName);
            }
            
            return acquired;
            
        } catch (Exception e) {
            logger.error("分布式限流器 {} 执行异常", limiterName, e);
            // 异常情况下允许通过，避免影响业务
            return true;
        }
    }
    
    /**
     * 获取限流器状态信息
     * @param limiterName 限流器名称
     * @return 状态信息
     */
    public RateLimiterStatus getStatus(String limiterName) {
        try {
            String key = "jingyu:ratelimiter:" + limiterName;
            RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
            
            if (!rateLimiter.isExists()) {
                return new RateLimiterStatus(limiterName, false, 0, 0);
            }
            
            long availablePermits = rateLimiter.availablePermits();
            RRateLimiter.RateLimiterConfig config = rateLimiter.getConfig();
            
            return new RateLimiterStatus(limiterName, true, availablePermits, config.getRate());
            
        } catch (Exception e) {
            logger.error("获取分布式限流器 {} 状态异常", limiterName, e);
            return new RateLimiterStatus(limiterName, false, 0, 0);
        }
    }
    
    /**
     * 重置限流器
     * @param limiterName 限流器名称
     */
    public void reset(String limiterName) {
        try {
            String key = "jingyu:ratelimiter:" + limiterName;
            RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
            rateLimiter.delete();
            logger.info("重置分布式限流器: {}", limiterName);
        } catch (Exception e) {
            logger.error("重置分布式限流器 {} 异常", limiterName, e);
        }
    }
    
    /**
     * 限流器状态信息
     */
    public static class RateLimiterStatus {
        private final String name;
        private final boolean exists;
        private final long availablePermits;
        private final long rateLimit;
        
        public RateLimiterStatus(String name, boolean exists, long availablePermits, long rateLimit) {
            this.name = name;
            this.exists = exists;
            this.availablePermits = availablePermits;
            this.rateLimit = rateLimit;
        }
        
        public String getName() {
            return name;
        }
        
        public boolean isExists() {
            return exists;
        }
        
        public long getAvailablePermits() {
            return availablePermits;
        }
        
        public long getRateLimit() {
            return rateLimit;
        }
    }
}
