package com.leiting.ltvip.service.jingyu.handler;


import com.leiting.ltvip.model.vo.JingyuEventContent;
import com.leiting.ltvip.util.GsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 事件处理器
 * <AUTHOR>
 * @date 2025/5/19 15:38
 */
@Service
public class EventHandlerHolder {
    
    private static final Logger logger = LoggerFactory.getLogger(EventHandlerHolder.class);
    
    private static final Map<Integer, EventHandler> HANDLERS = new HashMap<>();
    
    // 注册
    public static void register(Integer eventType, EventHandler handler) {
        HANDLERS.put(eventType, handler);
    }
    
    // 处理
    public static void handle(String json) {
        try {
            JingyuEventContent content = GsonUtil.LOWER_CASE_WITH_UNDERSCORES.fromJson(json, JingyuEventContent.class);
            Integer eventType = content.getEventType();
            EventHandler handler = get(eventType);
            handler.handle(content);
        } catch (Exception e) {
            logger.error("鲸域 处理消息异常 json={}", json, e);
        }
    }
    
    // 获取事件处理器
    private static EventHandler get(Integer eventType) {
        return HANDLERS.getOrDefault(eventType, EventHandler.DO_NOTING);
    }
    
}
