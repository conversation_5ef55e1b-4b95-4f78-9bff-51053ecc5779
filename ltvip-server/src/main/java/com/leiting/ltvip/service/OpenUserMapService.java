package com.leiting.ltvip.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.leiting.framework.base.PreCheck;
import com.leiting.guise.CopyObjectUtil;
import com.leiting.ltvip.constant.RedisConst;
import com.leiting.ltvip.exception.LtVipException;
import com.leiting.ltvip.mapper.ltVipDB.OpenUserMapMapper;
import com.leiting.ltvip.model.OpenUserMap;
import com.leiting.ltvip.model.po.OpenUserMapPO;
import com.leiting.ltvip.model.vo.OpenUserMapVO;
import com.leiting.ltvip.service.common.StringRedisService;
import com.leiting.ltvip.util.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 微信用户openId绑定信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Service
public class OpenUserMapService {
    private final Logger logger = LoggerFactory.getLogger(OpenUserMapService.class);

    @Autowired
    private OpenUserMapMapper openUserMapMapper;

    @Autowired
    private StringRedisService stringRedisService;

    public PageInfo<OpenUserMapPO> getPageInfoByVo(OpenUserMapVO vo){
        PreCheck.checkNotBlank(vo.getOpenId(), "未传入openId");

        PageInfo<OpenUserMapPO> pageInfo = new PageInfo<>();
        int total = this.getCountByVO(vo);
        List<OpenUserMapPO> list = null;
        if (total > 0) {
            list = this.getListPOByVO(vo);
        }

        if(CollectionUtils.isNotEmpty(list)){
            pageInfo.setList(list);
        }else{
            pageInfo.setList(new ArrayList<>());
        }

        pageInfo.setTotal(total);
        pageInfo.setPageSize(vo.getPageSize());
        pageInfo.setPageNum(vo.getPage());
        return pageInfo;
    }

    public List<OpenUserMap> getListByVO(OpenUserMapVO vo) {
        PreCheck.checkNotBlank(vo.getOpenId(), "未传入openId");

        Example example = new Example(OpenUserMap.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("openId", vo.getOpenId());

        if (!vo.isPager()) {
            if (BeanUtil.specificFieldsIsNull(vo, "searchStartDate", "searchEndDate", "openId", "appId")) {
                logger.error("OpenUserMapService danger query:" + vo);
                return null;
            }
        }

        if (StringUtils.isNotBlank(vo.getAppId())) {
            criteria.andEqualTo("appId", vo.getAppId());
        }

        if (StringUtils.isNotEmpty(vo.getUsername())) {
            String userId = stringRedisService.getUserIdByUsernameNew(vo.getUsername());
            if (StringUtils.isBlank(userId)) {
                return new ArrayList<>();
            }
            vo.setUserId(userId);
        }
        if (StringUtils.isNotEmpty(vo.getUserId())) {
            criteria.andEqualTo("userId", vo.getUserId());
        }

        if(StringUtils.isNotEmpty(vo.getSearchStartDate())) {
            criteria.andGreaterThanOrEqualTo("createDate", vo.getSearchStartDate());
        }
        if(StringUtils.isNotEmpty(vo.getSearchEndDate())) {
            criteria.andLessThanOrEqualTo("createDate", vo.getSearchEndDate());
        }
        if (vo.isPager() && vo.getPage() != null && vo.getPageSize() != null) {
            PageHelper.startPage(vo.getPage(), vo.getPageSize(), false);
        }
        if (vo.isUseSort()) {
            example.setOrderByClause(vo.getSortName() + " " + vo.getSortOrder());
        }
        return openUserMapMapper.selectByExample(example);
    }

    public List<OpenUserMapPO> getListPOByVO(OpenUserMapVO vo) {
        List<OpenUserMap> list = this.getListByVO(vo);

        List<OpenUserMapPO> poList = new ArrayList<>();

        for (OpenUserMap userMap : list) {
            try {
                OpenUserMapPO po =
                        CopyObjectUtil.copyObject(userMap, OpenUserMapPO.class);
                poList.add(po);
                String userId = userMap.getUserId();
                String username = stringRedisService.getUsernameByUserIdNew(userId);
                po.setUsername(username);
            }
            catch (Exception e) {
                logger.error("OpenUserMap getListByVO error:", e);
            }
        }

        return poList;
    }

    public int getCountByVO(OpenUserMapVO vo) {
        PreCheck.checkNotBlank(vo.getOpenId(), "未传入openId");

        Example example = new Example(OpenUserMap.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("openId", vo.getOpenId());

        if (StringUtils.isNotBlank(vo.getAppId())) {
            criteria.andEqualTo("appId", vo.getAppId());
        }

        if (StringUtils.isNotEmpty(vo.getUsername())) {
            String userId = stringRedisService.getUserIdByUsernameNew(vo.getUsername());
            if (StringUtils.isBlank(userId)) {
                return 0;
            }
            vo.setUserId(userId);
        }
        if (StringUtils.isNotEmpty(vo.getUserId())) {
            criteria.andEqualTo("userId", vo.getUserId());
        }

        if(StringUtils.isNotEmpty(vo.getSearchStartDate())) {
            criteria.andGreaterThanOrEqualTo("createDate", vo.getSearchStartDate());
        }
        if(StringUtils.isNotEmpty(vo.getSearchEndDate())) {
            criteria.andLessThanOrEqualTo("createDate", vo.getSearchEndDate());
        }

        return openUserMapMapper.selectCountByExample(example);
    }

    public Long addEntity(OpenUserMap entity) {
        PreCheck.checkNotBlank(entity.getUserId());
        PreCheck.checkNotBlank(entity.getOpenId());
        PreCheck.checkNotBlank(entity.getAppId());
        OpenUserMap existEntity = this.getSame(entity.getUserId(),
                entity.getOpenId(), entity.getAppId());
        if (existEntity != null) {
            throw new LtVipException("已存在绑定信息");
        }
        Date now = new Date();
        entity.setCreateDate(now);
        entity.setModifyDate(now);
        openUserMapMapper.insertSelective(entity);
        return entity.getId();
    }

    public Long updateEntityById(OpenUserMap entity) {
        PreCheck.checkNotNull(entity.getId());
        PreCheck.checkNotBlank(entity.getOpenId());
        Date now = new Date();
        entity.setModifyDate(now);
        openUserMapMapper.updateByPrimaryKeySelective(entity);
        return entity.getId();
    }

    public OpenUserMap getSame(String userId, String openId, String appId) {
        PreCheck.checkNotBlank(userId);
        PreCheck.checkNotBlank(openId);
        PreCheck.checkNotBlank(appId);
        return openUserMapMapper.selectSame(userId, openId, appId);
    }

    public OpenUserMap getByOpenIdAndAppId(String openId, String appId) {
        PreCheck.checkNotBlank(openId);
        PreCheck.checkNotBlank(appId);
        return openUserMapMapper.selectByOpenIdAndAppId(openId, appId);
    }

    public boolean isBinding(String openId, String appId) {
        PreCheck.checkNotBlank(openId);
        PreCheck.checkNotBlank(appId);
        return this.getByOpenIdAndAppId(openId, appId) != null;
    }

    public void deleteByOpenId(String openId, String appId) {
        PreCheck.checkNotBlank(openId);
        PreCheck.checkNotBlank(appId);
        openUserMapMapper.deleteByOpenId(openId, appId);
    }

    public void clearByUserId(String userId) {
        PreCheck.checkNotBlank(userId);
        for (int i = 1; i <= 8; i++) {
            List<OpenUserMap> list = openUserMapMapper.getByUserId(userId, String.valueOf(i));
            if (CollectionUtils.isNotEmpty(list)) {
                for (OpenUserMap openUserMap : list) {
                    String openId = openUserMap.getOpenId();
                    String appId = openUserMap.getAppId();
                    String openCacheKey = RedisConst.OPEN_USERNAME_CACHE_KEY + appId + ":" + openId;
                    stringRedisService.del(openCacheKey);
                }
            }
            openUserMapMapper.deleteByUserId(userId, String.valueOf(i));
        }
    }

    public List<OpenUserMap> getByUserId(String userId) {
        PreCheck.checkNotBlank(userId);
        List<OpenUserMap> wxList = new ArrayList<>();
        for (int i = 1; i <= 8; i++) {
            List<OpenUserMap> list = openUserMapMapper.getByUserId(userId, String.valueOf(i));
            if (CollectionUtils.isNotEmpty(list)) {
                wxList.addAll(list);
            }
        }
        return wxList;
    }
    
    // 根据openId获取userId列表
    public List<String> getUserIds(String openId) {
        PreCheck.checkNotBlank(openId);
        return openUserMapMapper.getUserIds(openId);
    }
    
}