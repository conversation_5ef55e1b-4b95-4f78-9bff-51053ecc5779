package com.leiting.ltvip.service.jingyu;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.ratelimiter.RateLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 鲸域Resilience4j监控服务
 * 用于收集和记录限流器和熔断器的状态信息
 * <AUTHOR>
 * @date 2025/1/27
 */
@Service
public class JingyuResilienceMonitorService {
    
    private static final Logger logger = LoggerFactory.getLogger(JingyuResilienceMonitorService.class);
    
    @Autowired
    @Qualifier("jingyuSendRateLimiter")
    private RateLimiter sendRateLimiter;

    @Autowired
    @Qualifier("jingyuSendCircuitBreaker")
    private CircuitBreaker sendCircuitBreaker;
    
    /**
     * 定时收集监控指标（每分钟执行一次）
     */
    @Scheduled(fixedRate = 60000)
    public void collectMetrics() {
        try {
            Map<String, Object> metrics = new HashMap<>();
            
            // 收集发送接口的指标
            collectSendMetrics(metrics);
            
            // 收集认证接口的指标
            collectAuthMetrics(metrics);
            
            // 记录监控日志
            logger.info("鲸域Resilience4j监控指标: {}", metrics);
            
        } catch (Exception e) {
            logger.error("收集鲸域Resilience4j监控指标异常", e);
        }
    }
    
    /**
     * 收集发送接口的监控指标
     */
    private void collectSendMetrics(Map<String, Object> metrics) {
        // 限流器指标
        RateLimiter.Metrics sendRateLimiterMetrics = sendRateLimiter.getMetrics();
        Map<String, Object> sendRateLimit = new HashMap<>();
        sendRateLimit.put("availablePermissions", sendRateLimiterMetrics.getAvailablePermissions());
        sendRateLimit.put("numberOfWaitingThreads", sendRateLimiterMetrics.getNumberOfWaitingThreads());
        metrics.put("sendRateLimiter", sendRateLimit);
        
        // 熔断器指标
        CircuitBreaker.Metrics sendCircuitBreakerMetrics = sendCircuitBreaker.getMetrics();
        Map<String, Object> sendCircuitBreaker = new HashMap<>();
        sendCircuitBreaker.put("state", this.sendCircuitBreaker.getState().toString());
        sendCircuitBreaker.put("failureRate", sendCircuitBreakerMetrics.getFailureRate());
        sendCircuitBreaker.put("slowCallRate", sendCircuitBreakerMetrics.getSlowCallRate());
        sendCircuitBreaker.put("numberOfCalls", sendCircuitBreakerMetrics.getNumberOfBufferedCalls());
        sendCircuitBreaker.put("numberOfFailedCalls", sendCircuitBreakerMetrics.getNumberOfFailedCalls());
        sendCircuitBreaker.put("numberOfSlowCalls", sendCircuitBreakerMetrics.getNumberOfSlowCalls());
        sendCircuitBreaker.put("numberOfSuccessfulCalls", sendCircuitBreakerMetrics.getNumberOfSuccessfulCalls());
        metrics.put("sendCircuitBreaker", sendCircuitBreaker);
    }
    
    /**
     * 收集认证接口的监控指标
     */
    private void collectAuthMetrics(Map<String, Object> metrics) {
        // 限流器指标
        RateLimiter.Metrics authRateLimiterMetrics = authRateLimiter.getMetrics();
        Map<String, Object> authRateLimit = new HashMap<>();
        authRateLimit.put("availablePermissions", authRateLimiterMetrics.getAvailablePermissions());
        authRateLimit.put("numberOfWaitingThreads", authRateLimiterMetrics.getNumberOfWaitingThreads());
        metrics.put("authRateLimiter", authRateLimit);
        
        // 熔断器指标
        CircuitBreaker.Metrics authCircuitBreakerMetrics = authCircuitBreaker.getMetrics();
        Map<String, Object> authCircuitBreaker = new HashMap<>();
        authCircuitBreaker.put("state", this.authCircuitBreaker.getState().toString());
        authCircuitBreaker.put("failureRate", authCircuitBreakerMetrics.getFailureRate());
        authCircuitBreaker.put("slowCallRate", authCircuitBreakerMetrics.getSlowCallRate());
        authCircuitBreaker.put("numberOfCalls", authCircuitBreakerMetrics.getNumberOfBufferedCalls());
        authCircuitBreaker.put("numberOfFailedCalls", authCircuitBreakerMetrics.getNumberOfFailedCalls());
        authCircuitBreaker.put("numberOfSlowCalls", authCircuitBreakerMetrics.getNumberOfSlowCalls());
        authCircuitBreaker.put("numberOfSuccessfulCalls", authCircuitBreakerMetrics.getNumberOfSuccessfulCalls());
        metrics.put("authCircuitBreaker", authCircuitBreaker);
    }
    
    /**
     * 获取发送接口限流器状态
     */
    public Map<String, Object> getSendRateLimiterStatus() {
        RateLimiter.Metrics metrics = sendRateLimiter.getMetrics();
        Map<String, Object> status = new HashMap<>();
        status.put("name", "jingyuSend");
        status.put("availablePermissions", metrics.getAvailablePermissions());
        status.put("numberOfWaitingThreads", metrics.getNumberOfWaitingThreads());
        return status;
    }
    
    /**
     * 获取发送接口熔断器状态
     */
    public Map<String, Object> getSendCircuitBreakerStatus() {
        CircuitBreaker.Metrics metrics = sendCircuitBreaker.getMetrics();
        Map<String, Object> status = new HashMap<>();
        status.put("name", "jingyuSend");
        status.put("state", sendCircuitBreaker.getState().toString());
        status.put("failureRate", metrics.getFailureRate());
        status.put("slowCallRate", metrics.getSlowCallRate());
        status.put("numberOfCalls", metrics.getNumberOfBufferedCalls());
        status.put("numberOfFailedCalls", metrics.getNumberOfFailedCalls());
        status.put("numberOfSlowCalls", metrics.getNumberOfSlowCalls());
        status.put("numberOfSuccessfulCalls", metrics.getNumberOfSuccessfulCalls());
        return status;
    }
    
    /**
     * 获取认证接口限流器状态
     */
    public Map<String, Object> getAuthRateLimiterStatus() {
        RateLimiter.Metrics metrics = authRateLimiter.getMetrics();
        Map<String, Object> status = new HashMap<>();
        status.put("name", "jingyuAuth");
        status.put("availablePermissions", metrics.getAvailablePermissions());
        status.put("numberOfWaitingThreads", metrics.getNumberOfWaitingThreads());
        return status;
    }
    
    /**
     * 获取认证接口熔断器状态
     */
    public Map<String, Object> getAuthCircuitBreakerStatus() {
        CircuitBreaker.Metrics metrics = authCircuitBreaker.getMetrics();
        Map<String, Object> status = new HashMap<>();
        status.put("name", "jingyuAuth");
        status.put("state", authCircuitBreaker.getState().toString());
        status.put("failureRate", metrics.getFailureRate());
        status.put("slowCallRate", metrics.getSlowCallRate());
        status.put("numberOfCalls", metrics.getNumberOfBufferedCalls());
        status.put("numberOfFailedCalls", metrics.getNumberOfFailedCalls());
        status.put("numberOfSlowCalls", metrics.getNumberOfSlowCalls());
        status.put("numberOfSuccessfulCalls", metrics.getNumberOfSuccessfulCalls());
        return status;
    }
    
    /**
     * 获取所有监控状态
     */
    public Map<String, Object> getAllStatus() {
        Map<String, Object> allStatus = new HashMap<>();
        allStatus.put("sendRateLimiter", getSendRateLimiterStatus());
        allStatus.put("sendCircuitBreaker", getSendCircuitBreakerStatus());
        allStatus.put("authRateLimiter", getAuthRateLimiterStatus());
        allStatus.put("authCircuitBreaker", getAuthCircuitBreakerStatus());
        return allStatus;
    }
    
    /**
     * 手动重置熔断器状态（紧急情况下使用）
     */
    public void resetCircuitBreakers() {
        logger.warn("手动重置鲸域熔断器状态");
        sendCircuitBreaker.reset();
        authCircuitBreaker.reset();
    }
}
