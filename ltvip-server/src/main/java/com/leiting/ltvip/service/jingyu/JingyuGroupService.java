package com.leiting.ltvip.service.jingyu;


import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.leiting.config.api.anotation.LtValue;
import com.leiting.ltvip.constant.JingyuURI;
import com.leiting.ltvip.util.GsonUtil;
import com.leiting.tools.base.annotation.Nullable;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 鲸域分组服务类
 * <AUTHOR>
 * @date 2025/5/21 16:52
 */
@Service
public class JingyuGroupService {
    
    private static final Logger logger = LoggerFactory.getLogger(JingyuGroupService.class);
    
    @Autowired
    private JingyuSendService jingyuSendService;
    
    private static final String PREFIX_GROUP = "group"; // 群聊receiverId前缀
    
    // 分组对应AI应用映射关系
    private final Map<String, AppConfig> CATEGORY_APPCONFIG_MAPPING = new HashMap<>();
    
    // 接收者id对应分组映射关系
    private final LoadingCache<String, String> RECEIVERID_CATEGORY_MAPPING = CacheBuilder.newBuilder()
                                                                                         .initialCapacity(1000).maximumSize(1000)
                                                                                         // 设置最大支持并发数量，默认为4可能会造成部分线程阻塞
                                                                                         .concurrencyLevel(16)
                                                                                         // 设置写缓存1分钟后过期
                                                                                         .expireAfterWrite(1, TimeUnit.DAYS)
                                                                                         .build(new CacheLoader<String, String>() {
                                                                                             @Override
                                                                                             public String load(@NotNull String receiverId) {
                                                                                                 if (StringUtils.startsWith(receiverId, PREFIX_GROUP)) {
                                                                                                     return queryGroupCategory(receiverId);
                                                                                                 } else {
                                                                                                     return queryRobotCategory(receiverId);
                                                                                                 }
                                                                                             }
                                                                                         });
    
    @LtValue("jingyu.app.configs") // 鲸域AI应用配置，json数组
    public void initAppConfig(String json) {
        try {
            List<AppConfig> list = GsonUtil.fromJson(json, new TypeToken<List<AppConfig>>() {
            }.getType());
            if (!CollectionUtils.isEmpty(list)) {
                for (AppConfig appConfig : list) {
                    for (String category : appConfig.getCategory()) {
                        if (StringUtils.isNotBlank(category)) {
                            CATEGORY_APPCONFIG_MAPPING.put(category, appConfig);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("鲸域 初始化应用配置 异常 json={}", json, e);
        }
    }
    
    // 根据消息通知中接收者id获取对应应用配置
    @Nullable
    public AppConfig detectAppConfig(String receiverId) {
        if (StringUtils.isBlank(receiverId)) {
            return null;
        }
        
        String category = queryCategoryWithCache(receiverId);
        return CATEGORY_APPCONFIG_MAPPING.get(category);
    }
    
    // 带缓存获取分组信息
    public String queryCategoryWithCache(String receiverId) {
        try {
            return RECEIVERID_CATEGORY_MAPPING.get(receiverId);
        } catch (Exception e) {
            logger.error("鲸域 获取分组信息 异常 receiverId={}", receiverId, e);
            return "";
        }
    }
    
    // 查询机器人分组信息
    @SuppressWarnings("unchecked")
    private String queryRobotCategory(String robotId) {
        Map<String, List<String>> params = new HashMap<>();
        params.put("robot_id_list", Lists.newArrayList(robotId));
        String json = GsonUtil.toJson(params);
        String response = jingyuSendService.request(JingyuURI.GET_ROBOT_CONF_LIST, json);
        try {
            Map<String, Object> result = GsonUtil.jsonToMap(response);
            if (result != null && (Integer) result.get("errcode") == 0) {
                Map<String, Object> outerData = (Map<String, Object>) result.get("data");
                if (outerData != null) {
                    Map<String, String> map = (Map<String, String>) outerData.get("robot_group_map");
                    if (!CollectionUtils.isEmpty(map)) { // 获取成功
                        return map.entrySet().stream()
                                  .filter(entry -> StringUtils.equals(robotId, entry.getKey()))
                                  .findFirst().map(Map.Entry::getValue).orElse(null);
                    }
                }
            }
            
            logger.error("鲸域 查询机器人分组 失败 robotId={} response={}", robotId, response);
        } catch (Exception e) {
            logger.error("鲸域 查询机器人分组 异常 robotId={}", robotId, e);
        }
        return null;
    }
    
    // 查询群聊分组信息
    @SuppressWarnings("unchecked")
    private String queryGroupCategory(String groupId) {
        Map<String, List<String>> params = new HashMap<>();
        params.put("group_id_list", Lists.newArrayList(groupId));
        String json = GsonUtil.toJson(params);
        String response = jingyuSendService.request(JingyuURI.GET_CATEGORY_BY_GROUP, json);
        try {
            Map<String, Object> result = GsonUtil.jsonToMap(response);
            if (result != null && (Integer) result.get("errcode") == 0) {
                Map<String, Object> outerData = (Map<String, Object>) result.get("data");
                if (outerData != null) {
                    List<Map<String, String>> list = (List<Map<String, String>>) outerData.get("item_list");
                    if (!CollectionUtils.isEmpty(list)) { // 获取成功
                        return list.stream().filter(map -> StringUtils.equals(groupId, map.get("group_id")))
                                   .findFirst().map(map -> map.get("name"))
                                   .orElse(null);
                    }
                }
            }
            logger.error("鲸域 查询群聊分组 失败 groupId={} response={}", groupId, response);
        } catch (Exception e) {
            logger.error("鲸域 查询群聊分组 异常 groupId={}", groupId, e);
        }
        return null;
    }
    
    // AI应用配置
    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AppConfig {
        private String workspace; // 工作空间(即游戏标识)
        private String appId; // 百炼应用appId
        private List<String> category; // 该应用对应的分组列表
    }
    
}
