package com.leiting.ltvip.service.jingyu.handler;


import com.google.common.reflect.TypeToken;
import com.leiting.config.api.anotation.LtValue;
import com.leiting.ltvip.constant.JingyuEventTypeEnum;
import com.leiting.ltvip.constant.JingyuURI;
import com.leiting.ltvip.model.vo.JingyuEventContent;
import com.leiting.ltvip.service.jingyu.JingyuSendService;
import com.leiting.ltvip.util.GsonUtil;
import com.leiting.ltvip.util.JingyuUtil;
import com.leiting.tools.time.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发送群聊消息处理器
 * https://open.xunjinet.com.cn/md/02-%E6%B6%88%E6%81%AF%E7%9B%B8%E5%85%B3/2001-SendMsgToGroup__0.html
 * <AUTHOR>
 * @date 2025/5/19 16:14
 */
@Service
public class SendGroupHandler extends BaseEventHandler implements EventHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(SendGroupHandler.class);
    
    @Autowired
    private JingyuSendService jingyuSendService;
    
    private List<String> greyGroups = new ArrayList<>();
    
    @LtValue("jingyu.grey.groups")
    public void initGreyGroups(String json) { // 鲸域测试企微群聊，json数组
        try {
            List<Group> list = GsonUtil.fromJson(json, new TypeToken<List<Group>>() {
            }.getType());
            if (!CollectionUtils.isEmpty(list)) {
                greyGroups = list.stream().map(Group::getGroupId).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("鲸域 初始化灰度群聊 异常 json={}", json, e);
        }
    }
    
    @Override
    @PostConstruct
    public void register() {
        EventHandlerHolder.register(JingyuEventTypeEnum.PASSIVE_GROUP_MESSAGE.getEventType(), this);
    }
    
    // 是否需要回复
    public boolean needReplay(JingyuEventContent content) {
        // 只处理艾特机器人的，且只对测试环境测试群组发送
        return isAtRobot(content) && isGreyGroup(content);
    }
    
    // 是否为艾特群聊中机器人
    private boolean isAtRobot(JingyuEventContent content) {
        if (StringUtils.isBlank(content.getAtList())) {
            return false;
        }
        List<String> atUsers = Arrays.asList(StringUtils.split(content.getAtList(), ";,"));
        return atUsers.contains(content.getRobotId());
    }
    
    // 是否为灰度测试区组
    private boolean isGreyGroup(JingyuEventContent content) {
        return greyGroups.contains(content.getReceiverId());
    }
    
    public void doSend(JingyuEventContent content, String answer) {
        if (StringUtils.isBlank(answer)) {
            return;
        }
        
        Map<String, Object> params = new HashMap<>();
        params.put("robot_id", content.getRobotId());
        params.put("group_id", content.getReceiverId());
        params.put("msg_id", JingyuUtil.genMsgId());
        params.put("dead_line", DateUtil.addMinutes(new Date(), 5).getTime() / 1000); // 秒时间戳
        List<Map<String, Object>> msgList = new ArrayList<>();
        msgList.add(msgItem(1, answer));
        params.put("msg_list", msgList);
        String json = GsonUtil.toJson(params);
        String response = jingyuSendService.request(JingyuURI.SEND_MSG_TO_GROUP, json);
        if (!isSuccess(response)) {
            logger.error("鲸域 发送群聊消息 失败 uri={} params={} response={}", JingyuURI.SEND_MSG_TO_GROUP.getUri(), json, response);
        }
    }
    
    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    static class Group {
        
        private String name; // 群聊名
        private String groupId; // 群id
        
    }
    
}
