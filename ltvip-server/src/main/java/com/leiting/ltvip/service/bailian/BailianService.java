package com.leiting.ltvip.service.bailian;


import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.google.common.reflect.TypeToken;
import com.leiting.config.api.anotation.LtValue;
import com.leiting.ltvip.constant.CommonConst;
import com.leiting.ltvip.exception.CallModelException;
import com.leiting.ltvip.service.common.StringRedisService;
import com.leiting.ltvip.util.GsonUtil;
import com.leiting.tools.base.annotation.Nullable;
import io.reactivex.Flowable;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 阿里云百炼服务类
 * <a href="https://help.aliyun.com/zh/model-studio/user-guide/application-calling/?spm=0.0.0.i3">接口文档</a>
 * <AUTHOR>
 * @date 2025/3/25 11:09
 */
@Service
public class BailianService {
    
    private static final Logger logger = LoggerFactory.getLogger(BailianService.class);
    
    private static final Map<String, String> WORKSPACE_APIKEY_MAP = new HashMap<>();
    
    @LtValue("model.workspace.apikey") // 阿里云百炼工作空间对应的apiKey，json数组
    public void initWorkspaceApiKey(String json) {
        try {
            List<WorkspaceApiKey> list = GsonUtil.fromJson(json, new TypeToken<List<WorkspaceApiKey>>() {
            }.getType());
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (WorkspaceApiKey item : list) {
                WORKSPACE_APIKEY_MAP.put(item.getWorkspace(), item.getApiKey());
            }
        } catch (Exception e) {
            logger.error("百炼 初始化apiKey异常 json={}", json, e);
        }
    }
    
    @Getter
    @LtValue("model.exception.message")
    private String exceptionMessage; // 调用阿里云百炼异常默认提示信息（对玩家展示）
    
    @Value("${env}")
    private String env;
    
    @Autowired
    private StringRedisService redisService;
    
    /**
     * 一次性输出
     *
     * @param workspace
     * @param appId             阿里云百炼应用ID
     * @param externalSessionId 外部会话sessionId
     * @param question          问题
     * @return 大模型回复信息
     */
    public String chat(String workspace, String appId, Long externalSessionId, String question) throws CallModelException {
        try {
            String localSessionId = getLocalSessionId(externalSessionId);
            ApplicationParam param = ApplicationParam.builder()
                                                     .appId(appId)
                                                     .apiKey(getApiKey(workspace))
                                                     .sessionId(localSessionId)
                                                     .prompt(question)
                                                     .build();
            Application application = new Application();
            ApplicationResult result = application.call(param);
            // 记录sessionId映射关系，维持会话记忆
            saveSessionIdMapping(externalSessionId, result.getOutput().getSessionId());
            return result.getOutput().getText();
        } catch (Exception e) {
            logger.error("百炼 单轮对话 异常 question={}", question, e);
            throw new CallModelException(exceptionMessage);
        }
    }
    
    // 流式输出
    public Flowable<ApplicationResult> chatStream(String workspace, String appId, Long externalSessionId, String question) throws CallModelException {
        try {
            String localSessionId = getLocalSessionId(externalSessionId);
            ApplicationParam param = ApplicationParam.builder()
                                                     .appId(appId)
                                                     .apiKey(getApiKey(workspace))
                                                     .sessionId(localSessionId)
                                                     .prompt(question)
                                                     .incrementalOutput(true) // 开启增量输出，每次只显示最新的输出
                                                     .build();
            Application application = new Application();
            // 使用 AtomicBoolean 标记是否已处理第一个响应
            AtomicBoolean firstProcessed = new AtomicBoolean(false);
            Flowable<ApplicationResult> call = application.streamCall(param);
            return call.doOnNext(result -> {
                if (!firstProcessed.getAndSet(true)) { // 仅第一次触发
                    saveSessionIdMapping(externalSessionId, result.getOutput().getSessionId());
                }
            });
        } catch (Exception e) {
            logger.error("百炼 流式输出 异常 question={}", question, e);
            throw new CallModelException(exceptionMessage);
        }
    }
    
    // 通过工作空间获取对应的apiKey
    private String getApiKey(String workspace) {
        if (StringUtils.isBlank(workspace)) {
            return "";
        }
        return WORKSPACE_APIKEY_MAP.getOrDefault(workspace, "");
    }
    
    // 传入的工作空间有效
    public boolean isValidWorkspace(String workspace) {
        return StringUtils.isNotBlank(getApiKey(workspace));
    }
    
    // 通过外部sessionId获取对应的本服务sessionId
    @Nullable
    private String getLocalSessionId(@Nullable Long externalSessionId) {
        if (externalSessionId == null) {
            return null;
        }
        String localSessionId = redisService.get(String.valueOf(externalSessionId));
        return StringUtils.isBlank(localSessionId) ? null : localSessionId;
    }
    
    // 保存sessionId映射关系
    private void saveSessionIdMapping(Long externalSessionId, String localSessionId) {
        if (externalSessionId == null || StringUtils.isBlank(localSessionId)) {
            return;
        }
        redisService.putWithExpire(String.valueOf(externalSessionId), localSessionId, 30, TimeUnit.MINUTES);
    }
    
    // 格式化回复信息
    public String formatReply(String input) {
        if (StringUtils.isBlank(input)) {
            return exceptionMessage;
        }
        
        int index = input.indexOf("【正式回复】"); // 定位正式回复所在位置
        if (index == -1) {
            return clearRef(input); // 无【正式回复】则返回原内容
        }
        // 有正式回复
        String normalReply = clearRef(changeTag(input.substring(index))); // 正式回复部分需要去除标签
        if (CommonConst.ENV_PROD.equals(env)) { // 正式环境只取正式回复
            return normalReply;
        } else { // 开发测试环境取全部
            return input.substring(0, index) + normalReply;
        }
    }
    
    // 调整标签
    private String changeTag(String input) {
        return StringUtils.replaceAll(input, "【正式回复】", "【智能ai回复】");
    }
    
    // 清理<ref>标签
    private String clearRef(String input) {
        String ref = "(?:<ref>.*?</ref>)?";
        return StringUtils.replaceAll(input, ref, "");
    }
    
    // 工作空间apiKey
    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    static class WorkspaceApiKey {
        private String workspace; // 工作空间，与七鱼后台链接需要配置一致
        private String apiKey; // 对应工作空间的apiKey
    }
    
}
