package com.leiting.ltvip.service.jingyu.handler;


import com.leiting.config.api.anotation.LtValue;
import com.leiting.ltvip.model.vo.JingyuEventContent;
import com.leiting.ltvip.service.bailian.BailianService;
import com.leiting.ltvip.service.jingyu.JingyuGroupService;
import com.leiting.ltvip.util.GsonUtil;
import com.leiting.ltvip.util.JingyuUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 事件处理器公共逻辑
 * <AUTHOR>
 * @date 2025/5/19 16:32
 */
@Service
public abstract class BaseEventHandler implements EventHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(BaseEventHandler.class);
    
    @Autowired
    private JingyuGroupService jingyuGroupService;
    
    @Autowired
    private BailianService bailianService;
    
    private static Pattern UNMATCH_PATTERN; // 大模型未匹配格式
    
    private static String AI_HELPER_MESSAGE; // 玩家求助ai格式文本
    private static Pattern AI_HELPER_PATTERN; // 玩家求助ai格式
    
    @LtValue(value = "jingyu.send.config", defaultValue = "{}") // 鲸域发送配置，json格式，字段支持正则表达式
    public void initSendConfig(String json) {
        try {
            SendConfig config = GsonUtil.fromJson(json, SendConfig.class);
            if (config == null) {
                return;
            }
            if (StringUtils.isNotBlank(config.getUnmatchMessage())) {
                UNMATCH_PATTERN = Pattern.compile(config.getUnmatchMessage());
            }
            if (StringUtils.isNotBlank(config.getAiHelperMessage())) {
                AI_HELPER_MESSAGE = config.getAiHelperMessage();
                AI_HELPER_PATTERN = Pattern.compile(config.getAiHelperMessage());
            }
        } catch (Exception e) {
            logger.error("鲸域 初始化发送配置异常 json={}", json, e);
        }
    }
    
    // 实现类必须在启动的时候向 EventHandlerHolder 中注册自己
    public abstract void register();
    
    // 处理消息
    public void handle(JingyuEventContent content) {
        if (StringUtils.isBlank(content.getMsgContent())) {
            return;
        }
        if (!needReplay(content)) { // 不需要回复
            return;
        }
        // 用户AI求助请求
        boolean isAiHelper = AI_HELPER_PATTERN != null && AI_HELPER_PATTERN.matcher(content.getMsgContent()).find();
        // 格式化用户输入
        regularInput(content);
        String answer = aiChat(content);
        // 发送消息
        if (needSend(isAiHelper, answer)) {
            doSend(content, answer);
        }
    }
    
    // 是否需要回复
    public abstract boolean needReplay(JingyuEventContent content);
    
    // 规范化用户输入。子类覆盖的时候需要调用super.regularInput(content)
    public void regularInput(JingyuEventContent content) {
        String cleaned = StringUtils.remove(content.getMsgContent(), AI_HELPER_MESSAGE).trim();
        content.setMsgContent(cleaned);
    }
    
    // 是否需要发送消息
    public boolean needSend(boolean isAiHelper, String answer) {
        boolean modelMatched = UNMATCH_PATTERN == null || !UNMATCH_PATTERN.matcher(answer).find();
        if (modelMatched) { // 大模型匹配上了直接发送
            return true;
        }
        // 未匹配上，若用户提问为AI求助，则需要发送
        return isAiHelper;
    }
    
    // 发送消息
    public abstract void doSend(JingyuEventContent content, String answer);
    
    // 消息元素。msgNum范围为1~20
    protected Map<String, Object> msgItem(Integer msgNum, String msgContent) {
        Map<String, Object> item = new HashMap<>();
        item.put("msg_num", msgNum);
        item.put("msg_type", 1); // 发送文字消息
        item.put("msg_content", msgContent);
        return item;
    }
    
    // ai回复
    public String aiChat(JingyuEventContent content) {
        String receiverId = content.getReceiverId();
        JingyuGroupService.AppConfig config = jingyuGroupService.detectAppConfig(receiverId);
        if (config == null) {
            logger.error("鲸域 未匹配到标签 receiverId={} content={}", receiverId, content);
            return "";
        }
        
        return bailianService.chat(config.getWorkspace(), config.getAppId(), JingyuUtil.genSessionId(content.getSenderId()), content.getMsgContent());
    }
    
    // 判断是否成功
    public boolean isSuccess(String response) {
        if (StringUtils.isBlank(response)) {
            return false;
        }
        Map<String, Object> result = GsonUtil.jsonToMap(response);
        if (result == null) {
            return false;
        }
        Integer errcode = (Integer) result.get("errcode");
        return errcode == 0;
    }
    
    // 鲸域发送消息配置
    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    static class SendConfig {
        
        private String unmatchMessage; // 大模型未匹配格式，支持正则
        private String aiHelperMessage; // 玩家求助ai格式，支持正则
        
    }
    
}
