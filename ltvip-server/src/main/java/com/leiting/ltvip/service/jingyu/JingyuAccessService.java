package com.leiting.ltvip.service.jingyu;


import com.leiting.config.api.anotation.LtValue;
import com.leiting.ltvip.constant.JingyuURI;
import com.leiting.ltvip.util.GsonUtil;
import com.leiting.ltvip.util.OkHttpPoolUtil;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

/**
 * 鲸域认证服务类
 * <AUTHOR>
 * @date 2025/5/19 17:55
 */
@Service
public class JingyuAccessService {
    
    private static final Logger logger = LoggerFactory.getLogger(JingyuAccessService.class);
    
    @LtValue("jingyu.app.key") // 鲸域appKey
    private String appKey;
    
    @LtValue("jingyu.app.secret")  // 鲸域appSecret
    private String appSecret;
    
    // 类成员变量
    private static final AtomicReference<Pair<String, Integer>> TOKEN_CACHE = new AtomicReference<>();
    private static final long TOKEN_REFRESH_BUFFER = 300L; // 提前5分钟刷新token
    private static String CORP_ID = ""; // 企业id
    
    
    // 获取缓存的token
    public String getTokenWithCache() {
        Pair<String, Integer> tokenPair = TOKEN_CACHE.get();
        long currentTime = System.currentTimeMillis() / 1000;
        
        // 如果token不存在或即将过期，则重新获取
        if (tokenPair == null || tokenPair.getRight() - currentTime <= TOKEN_REFRESH_BUFFER) {
            tokenPair = getToken();
            if (StringUtils.isNotBlank(tokenPair.getLeft())) {
                TOKEN_CACHE.set(tokenPair);
            }
        }
        
        return tokenPair.getLeft();
    }
    
    // 获取缓存的企业id
    public String getCorpIdWithCache() {
        if (StringUtils.isBlank(CORP_ID)) {
            CORP_ID = getCorpId();
        }
        return CORP_ID;
    }
    
    // 获取请求token
    @SuppressWarnings("unchecked")
    private Pair<String, Integer> getToken() {
        String url = JingyuSendService.JINGYU_DOMAIN + JingyuURI.GET_ACCESS_TOKEN.getUri();
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appKey);
        params.put("app_secret", appSecret);
        try {
            String response = OkHttpPoolUtil.postJson(url, GsonUtil.toJson(params));
            Map<String, Object> result = GsonUtil.jsonToMap(response);
            if (result != null && (Integer) result.get("errcode") == 0) {
                Map<String, Object> outerData = (Map<String, Object>) result.get("data");
                if (outerData != null) {
                    Map<String, Object> data = (Map<String, Object>) outerData.get("data");
                    if (data != null) { // 获取成功
                        return Pair.of((String) data.get("access_token"), (Integer) data.get("expires_in"));
                    }
                }
            }
            
            logger.error("鲸域 获取token 失败 appKey={} response={}", appKey, response);
        } catch (IOException e) {
            logger.error("鲸域 获取token 异常 appKey={}", appKey, e);
        }
        return Pair.of("", 0);
    }
    
    // 获取企业id
    @SuppressWarnings("unchecked")
    private String getCorpId() {
        String url = JingyuSendService.JINGYU_DOMAIN + JingyuURI.GET_THIRD_APP_BIND_CORP.getUri();
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appKey);
        params.put("app_secret", appSecret);
        try {
            String response = OkHttpPoolUtil.postJson(url, GsonUtil.toJson(params));
            Map<String, Object> result = GsonUtil.jsonToMap(response);
            if (result != null && (Integer) result.get("errcode") == 0) {
                Map<String, Object> outerData = (Map<String, Object>) result.get("data");
                if (outerData != null) {
                    List<Map<String, Object>> corpList = (List<Map<String, Object>>) outerData.get("corp_list");
                    if (!CollectionUtils.isEmpty(corpList)) { // 获取成功
                        Map<String, Object> corp = corpList.get(0);
                        return (String) corp.get("corp_id");
                    }
                }
            }
            
            logger.error("鲸域 获取企业id 失败 appKey={} response={}", appKey, response);
        } catch (IOException e) {
            logger.error("鲸域 获取企业id 异常 appKey={}", appKey, e);
        }
        return "";
    }
    
}
