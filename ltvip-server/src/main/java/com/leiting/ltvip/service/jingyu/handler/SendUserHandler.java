package com.leiting.ltvip.service.jingyu.handler;


import com.google.common.reflect.TypeToken;
import com.leiting.config.api.anotation.LtValue;
import com.leiting.ltvip.constant.JingyuEventTypeEnum;
import com.leiting.ltvip.constant.JingyuURI;
import com.leiting.ltvip.model.vo.JingyuEventContent;
import com.leiting.ltvip.service.jingyu.JingyuSendService;
import com.leiting.ltvip.util.GsonUtil;
import com.leiting.ltvip.util.JingyuUtil;
import com.leiting.tools.time.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发送私聊消息处理器
 * <AUTHOR>
 * @date 2025/5/19 16:08
 */
@Service
public class SendUserHandler extends BaseEventHandler implements EventHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(SendUserHandler.class);
    
    @Autowired
    private JingyuSendService jingyuSendService;
    
    private List<String> greyRobots = new ArrayList<>();
    
    @LtValue("jingyu.grey.robots")
    public void initGreyRobots(String json) { // 鲸域测试企微机器人，json数组
        try {
            List<Robot> list = GsonUtil.fromJson(json, new TypeToken<List<Robot>>() {
            }.getType());
            if (!CollectionUtils.isEmpty(list)) {
                greyRobots = list.stream().map(Robot::getRobotId).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("鲸域 初始化灰度机器人 异常 json={}", json, e);
        }
    }
    
    @Override
    @PostConstruct
    public void register() {
        EventHandlerHolder.register(JingyuEventTypeEnum.PASSIVE_USER_MESSAGE.getEventType(), this);
    }
    
    // 是否需要回复
    public boolean needReplay(JingyuEventContent content) {
        // 只处理机器人接收到的消息，且对测试环境测试机器人发送
        return isReceiver(content) && isGreyRobot(content);
    }
    
    // 是否为接收者
    private boolean isReceiver(JingyuEventContent content) {
        return StringUtils.equals(content.getReceiverId(), content.getRobotId());
    }
    
    // 是否为灰度测试机器人
    private boolean isGreyRobot(JingyuEventContent content) {
        return greyRobots.contains(content.getReceiverId());
    }
    
    public void doSend(JingyuEventContent content, String answer) {
        if (StringUtils.isBlank(answer)) {
            return;
        }
        
        Map<String, Object> params = new HashMap<>();
        params.put("robot_id", content.getRobotId());
        params.put("account_id", content.getSenderId());
        params.put("msg_id", JingyuUtil.genMsgId());
        params.put("dead_line", DateUtil.addMinutes(new Date(), 5).getTime() / 1000); // 秒时间戳
        List<Map<String, Object>> msgList = new ArrayList<>();
        msgList.add(msgItem(1, answer));
        params.put("msg_list", msgList);
        String json = GsonUtil.toJson(params);
        String response = jingyuSendService.request(JingyuURI.SEND_MESSAGE_TO_ACCOUNT, json);
        if (!isSuccess(response)) {
            logger.error("鲸域 发送私聊消息 失败 uri={} params={} response={}", JingyuURI.SEND_MESSAGE_TO_ACCOUNT.getUri(), json, response);
        }
    }
    
    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    static class Robot {
        
        private String name; // 机器人名
        private String robotId; // 机器人id
        
    }
    
}
