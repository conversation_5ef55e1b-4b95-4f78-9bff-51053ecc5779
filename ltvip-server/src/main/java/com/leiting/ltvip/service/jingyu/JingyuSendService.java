package com.leiting.ltvip.service.jingyu;


import com.leiting.ltvip.constant.JingyuURI;
import com.leiting.ltvip.util.OkHttpPoolUtil;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import okhttp3.Headers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.function.Supplier;

/**
 * 鲸域发送消息服务类
 * <AUTHOR>
 * @date 2025/5/22 15:55
 */
@Service
public class JingyuSendService {
    
    private static final Logger logger = LoggerFactory.getLogger(JingyuSendService.class);
    
    @Autowired
    private JingyuAccessService jingyuAccessService;
    
    public static final String JINGYU_DOMAIN = "https://api.xunjinet.com.cn"; // 鲸域接口域名
    
    // 请求认证字段
    private static final String HEADER_TOKEN = "Token"; // 请求token
    private static final String HEADER_CORP_ID = "x-ql-corp-id"; // 请求公司corpId
    
    // 请求接口
    public String request(JingyuURI uriEnum, String params) {
        if (uriEnum.isRateLimit()) {
            // TODO 处理限流，超过一定量直接熔断
        }
        
        String url = JINGYU_DOMAIN + uriEnum.getUri();
        try {
            Headers headers = Headers.of(
                    HEADER_TOKEN, jingyuAccessService.getTokenWithCache(),
                    HEADER_CORP_ID, jingyuAccessService.getCorpIdWithCache()
            );
            return OkHttpPoolUtil.postJson(url, headers, params);
        } catch (Exception e) {
            logger.error("鲸域 发送消息 异常 url={} params={}", url, params, e);
            return "";
        }
    }
    
}
