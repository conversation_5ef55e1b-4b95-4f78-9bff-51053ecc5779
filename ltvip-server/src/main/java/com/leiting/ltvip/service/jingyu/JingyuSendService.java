package com.leiting.ltvip.service.jingyu;


import com.leiting.ltvip.constant.CommonConst;
import com.leiting.ltvip.constant.JingyuURI;
import com.leiting.ltvip.util.GsonUtil;
import com.leiting.ltvip.util.OkHttpPoolUtil;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import lombok.AllArgsConstructor;
import lombok.Data;
import okhttp3.Headers;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.function.Supplier;

/**
 * 鲸域发送消息服务类
 * <AUTHOR>
 * @date 2025/5/22 15:55
 */
@Service
public class JingyuSendService {

    private static final Logger logger = LoggerFactory.getLogger(JingyuSendService.class);

    @Autowired
    private JingyuAccessService jingyuAccessService;
    
    @Autowired
    @Qualifier("jingyuSendRateLimiter")
    private RateLimiter sendRateLimiter;

    @Autowired
    @Qualifier("jingyuSendCircuitBreaker")
    private CircuitBreaker sendCircuitBreaker;
    
    @Value("${env}")
    private String env;
    
    public static final String JINGYU_DOMAIN = "https://api.xunjinet.com.cn"; // 鲸域接口域名
    
    // 请求认证字段
    private static final String HEADER_TOKEN = "Token"; // 请求token
    private static final String HEADER_CORP_ID = "x-ql-corp-id"; // 请求公司corpId
    
    // 请求接口
    public String request(JingyuURI uriEnum, String params) {
        if (uriEnum.isRateLimit()) {
            // 使用Resilience4j实现分布式流量整型和熔断
            return executeWithResiliencePattern(uriEnum, params);
        }

        // 非限流接口直接调用
        return executeRequest(uriEnum, params);
    }

    /**
     * 使用Resilience4j模式执行请求（限流 + 熔断）
     */
    private String executeWithResiliencePattern(JingyuURI uriEnum, String params) {
        try {
            // 先通过限流器
            Supplier<String> decoratedSupplier = RateLimiter.decorateSupplier(sendRateLimiter, () -> {
                // 再通过熔断器
                Supplier<String> circuitBreakerSupplier = CircuitBreaker.decorateSupplier(sendCircuitBreaker,
                    () -> executeRequest(uriEnum, params));
                return circuitBreakerSupplier.get();
            });

            return decoratedSupplier.get();

        } catch (RequestNotPermitted e) {
            // 限流异常处理
            logger.warn("鲸域请求被限流 uri={} params={}", uriEnum.getUri(), params);
            return buildRateLimitResponse();

        } catch (CallNotPermittedException e) {
            // 熔断异常处理
            logger.warn("鲸域请求被熔断 uri={} params={}", uriEnum.getUri(), params);
            return buildCircuitBreakerResponse();

        } catch (Exception e) {
            // 其他异常处理
            logger.error("鲸域请求执行异常 uri={} params={}", uriEnum.getUri(), params, e);
            return buildErrorResponse();
        }
    }

    /**
     * 执行实际的HTTP请求
     */
    private String executeRequest(JingyuURI uriEnum, String params) {
        // 开发环境直接返回成功
        if (StringUtils.equals(env, CommonConst.ENV_DEV)) {
            return buildSuccessResponse();
        }
        
        String url = JINGYU_DOMAIN + uriEnum.getUri();
        try {
            Headers headers = Headers.of(
                    HEADER_TOKEN, jingyuAccessService.getTokenWithCache(),
                    HEADER_CORP_ID, jingyuAccessService.getCorpIdWithCache()
            );
            String response = OkHttpPoolUtil.postJson(url, headers, params);
            logger.debug("鲸域请求成功 uri={} response={}", uriEnum.getUri(), response);
            return response;
        } catch (Exception e) {
            logger.error("鲸域 发送消息 异常 url={} params={}", url, params, e);
            throw new RuntimeException("鲸域API调用失败", e);
        }
    }

    /**
     * 构建限流响应
     */
    private String buildRateLimitResponse() {
        return JingyuResponse.fail(429, "请求过于频繁，请稍后重试").toJson();
    }

    /**
     * 构建熔断响应
     */
    private String buildCircuitBreakerResponse() {
        return JingyuResponse.fail(503, "服务暂时不可用，请稍后重试").toJson();
    }

    /**
     * 构建错误响应
     */
    private String buildErrorResponse() {
        return JingyuResponse.fail(500, "系统内部错误").toJson();
    }

    /**
     * 构建成功响应（开发环境使用）
     */
    private String buildSuccessResponse() {
        return JingyuResponse.success(null).toJson();
    }

    /**
     * 鲸域API响应结构
     * @param <T> data字段的类型
     */
    @Data
    @AllArgsConstructor
    private static class JingyuResponse<T> {
        private final int errcode;
        private final String errmsg;
        private final T data;

        /**
         * 转换为JSON字符串
         */
        public String toJson() {
            return GsonUtil.toJson(this);
        }

        /**
         * 创建成功响应
         */
        public static <T> JingyuResponse<T> success(T data) {
            return new JingyuResponse<>(0, "success", data);
        }

        /**
         * 创建限流错误响应
         */
        public static JingyuResponse<Object> rateLimitError() {
            return new JingyuResponse<>(429, "请求过于频繁，请稍后重试", null);
        }

        /**
         * 创建熔断错误响应
         */
        public static JingyuResponse<Object> circuitBreakerError() {
            return new JingyuResponse<>(503, "服务暂时不可用，请稍后重试", null);
        }

        /**
         * 创建系统错误响应
         */
        public static JingyuResponse<Object> systemError() {
            return new JingyuResponse<>(500, "系统内部错误", null);
        }

        /**
         * 创建自定义错误响应
         */
        public static <T> JingyuResponse<T> error(int errcode, String errmsg, T data) {
            return new JingyuResponse<>(errcode, errmsg, data);
        }
    }

}
