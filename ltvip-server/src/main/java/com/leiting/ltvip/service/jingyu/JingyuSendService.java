package com.leiting.ltvip.service.jingyu;


import com.leiting.ltvip.annotation.JingyuResilience;
import com.leiting.ltvip.constant.CommonConst;
import com.leiting.ltvip.constant.JingyuURI;
import com.leiting.ltvip.model.response.JingyuResponse;
import com.leiting.ltvip.util.OkHttpPoolUtil;
import okhttp3.Headers;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 鲸域发送消息服务类
 * <AUTHOR>
 * @date 2025/5/22 15:55
 */
@Service
public class JingyuSendService {

    private static final Logger logger = LoggerFactory.getLogger(JingyuSendService.class);

    @Autowired
    private JingyuAccessService jingyuAccessService;
    
    @Value("${env}")
    private String env;
    
    public static final String JINGYU_DOMAIN = "https://api.xunjinet.com.cn"; // 鲸域接口域名
    
    // 请求认证字段
    private static final String HEADER_TOKEN = "Token"; // 请求token
    private static final String HEADER_CORP_ID = "x-ql-corp-id"; // 请求公司corpId
    
    // 请求接口
    @JingyuResilience
    public String request(JingyuURI uriEnum, String params) {
        return executeRequest(uriEnum, params);
    }

    /**
     * 执行实际的HTTP请求
     */
    private String executeRequest(JingyuURI uriEnum, String params) {
        // 开发环境直接返回成功
        if (StringUtils.equals(env, CommonConst.ENV_DEV)) {
            return JingyuResponse.success(null).toJson();
        }
        String url = JINGYU_DOMAIN + uriEnum.getUri();
        try {
            Headers headers = Headers.of(
                    HEADER_TOKEN, jingyuAccessService.getTokenWithCache(),
                    HEADER_CORP_ID, jingyuAccessService.getCorpIdWithCache()
            );
            String response = OkHttpPoolUtil.postJson(url, headers, params);
            logger.debug("鲸域请求成功 uri={} response={}", uriEnum.getUri(), response);
            return response;
        } catch (Exception e) {
            logger.error("鲸域 发送消息 异常 url={} params={}", url, params, e);
            throw new RuntimeException("鲸域API调用失败", e);
        }
    }

}
