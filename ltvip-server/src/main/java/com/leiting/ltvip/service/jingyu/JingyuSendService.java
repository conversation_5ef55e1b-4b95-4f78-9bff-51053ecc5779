package com.leiting.ltvip.service.jingyu;


import com.leiting.ltvip.constant.JingyuURI;
import com.leiting.ltvip.util.OkHttpPoolUtil;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import okhttp3.Headers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.function.Supplier;

/**
 * 鲸域发送消息服务类
 * <AUTHOR>
 * @date 2025/5/22 15:55
 */
@Service
public class JingyuSendService {

    private static final Logger logger = LoggerFactory.getLogger(JingyuSendService.class);

    @Autowired
    private JingyuAccessService jingyuAccessService;

    @Autowired
    @Qualifier("jingyuSendRateLimiter")
    private RateLimiter sendRateLimiter;

    @Autowired
    @Qualifier("jingyuSendCircuitBreaker")
    private CircuitBreaker sendCircuitBreaker;
    
    public static final String JINGYU_DOMAIN = "https://api.xunjinet.com.cn"; // 鲸域接口域名
    
    // 请求认证字段
    private static final String HEADER_TOKEN = "Token"; // 请求token
    private static final String HEADER_CORP_ID = "x-ql-corp-id"; // 请求公司corpId
    
    // 请求接口
    public String request(JingyuURI uriEnum, String params) {
        if (uriEnum.isRateLimit()) {
            // 使用Resilience4j实现分布式流量整型和熔断
            return executeWithResiliencePattern(uriEnum, params);
        }

        // 非限流接口直接调用
        return executeRequest(uriEnum, params);
    }

    /**
     * 使用Resilience4j模式执行请求（限流 + 熔断）
     */
    private String executeWithResiliencePattern(JingyuURI uriEnum, String params) {
        try {
            // 先通过限流器
            Supplier<String> decoratedSupplier = RateLimiter.decorateSupplier(sendRateLimiter, () -> {
                // 再通过熔断器
                Supplier<String> circuitBreakerSupplier = CircuitBreaker.decorateSupplier(sendCircuitBreaker,
                    () -> executeRequest(uriEnum, params));
                return circuitBreakerSupplier.get();
            });

            return decoratedSupplier.get();

        } catch (RequestNotPermitted e) {
            // 限流异常处理
            logger.warn("鲸域请求被限流 uri={} params={}", uriEnum.getUri(), params);
            return buildRateLimitResponse();

        } catch (CallNotPermittedException e) {
            // 熔断异常处理
            logger.warn("鲸域请求被熔断 uri={} params={}", uriEnum.getUri(), params);
            return buildCircuitBreakerResponse();

        } catch (Exception e) {
            // 其他异常处理
            logger.error("鲸域请求执行异常 uri={} params={}", uriEnum.getUri(), params, e);
            return buildErrorResponse();
        }
    }

    /**
     * 执行实际的HTTP请求
     */
    private String executeRequest(JingyuURI uriEnum, String params) {
        String url = JINGYU_DOMAIN + uriEnum.getUri();
        try {
            Headers headers = Headers.of(
                    HEADER_TOKEN, jingyuAccessService.getTokenWithCache(),
                    HEADER_CORP_ID, jingyuAccessService.getCorpIdWithCache()
            );
            String response = OkHttpPoolUtil.postJson(url, headers, params);
            logger.debug("鲸域请求成功 uri={} response={}", uriEnum.getUri(), response);
            return response;
        } catch (Exception e) {
            logger.error("鲸域 发送消息 异常 url={} params={}", url, params, e);
            throw new RuntimeException("鲸域API调用失败", e);
        }
    }

    /**
     * 构建限流响应
     */
    private String buildRateLimitResponse() {
        return "{\"errcode\": 429, \"errmsg\": \"请求过于频繁，请稍后重试\", \"data\": null}";
    }

    /**
     * 构建熔断响应
     */
    private String buildCircuitBreakerResponse() {
        return "{\"errcode\": 503, \"errmsg\": \"服务暂时不可用，请稍后重试\", \"data\": null}";
    }

    /**
     * 构建错误响应
     */
    private String buildErrorResponse() {
        return "{\"errcode\": 500, \"errmsg\": \"系统内部错误\", \"data\": null}";
    }

}
