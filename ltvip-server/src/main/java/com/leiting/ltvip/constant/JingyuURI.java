package com.leiting.ltvip.constant;


import lombok.Getter;

/**
 * 鲸域URI接口地址
 *
 * <AUTHOR>
 * @date 2025/5/21 16:39
 */
@Getter
public enum JingyuURI {
    
    // https://open.xunjinet.com.cn/md/__04-%E7%AC%AC%E4%B8%89%E6%96%B9%E5%BA%94%E7%94%A8%E5%BC%80%E6%94%BE%E6%8E%A5%E5%8F%A3%E6%8E%A5%E5%85%A5.html
    GET_ACCESS_TOKEN("/gateway/qopen/GetAccessToken", "获取访问凭据", false),
    GET_THIRD_APP_BIND_CORP("/gateway/qopen/GetThirdAppBindCorpList", "获取第三方应用绑定的企业列表", false),
    
    // https://open.xunjinet.com.cn/md/09-%E5%A2%9E%E5%BC%BA%E5%BA%94%E7%94%A8/%E5%88%86%E7%BB%84/10002-GetRobotConfList.html
    GET_ROBOT_CONF_LIST("/gateway/eaopen/GetRobotConfList", "查询机器人分组", false),
    
    // https://open.xunjinet.com.cn/md/09-%E5%A2%9E%E5%BC%BA%E5%BA%94%E7%94%A8/%E5%88%86%E7%BB%84/10004-GetGroupCategoryByGroup.html
    GET_CATEGORY_BY_GROUP("/gateway/eaopen/GetCategoryByGroup", "查询群所属分组", false),
    
    // https://open.xunjinet.com.cn/md/02-%E6%B6%88%E6%81%AF%E7%9B%B8%E5%85%B3/2001-SendMsgToGroup__0.html
    SEND_MSG_TO_GROUP("/gateway/qopen/SendMsgToGroup", "发送群聊消息", true),
    
    // https://open.xunjinet.com.cn/md/02-%E6%B6%88%E6%81%AF%E7%9B%B8%E5%85%B3/2002-SendMessageToAccount__0.html
    SEND_MESSAGE_TO_ACCOUNT("/gateway/qopen/SendMessageToAccount", "发送私聊消息", true),
    ;
    
    private final String uri; // uri地址
    private final String info; // 接口说明
    private final boolean rateLimit; // 是否需要限流
    
    JingyuURI(String uri, String info, boolean rateLimit) {
        this.uri = uri;
        this.info = info;
        this.rateLimit = rateLimit;
    }
    
}
