package com.leiting.ltvip.constant;

import com.leiting.ltvip.model.response.JingyuResponse;

/**
 * 鲸域Resilience4j相关常量
 * <AUTHOR>
 * @date 2025/1/27
 */
public class JingyuResilienceConst {

    /**
     * 限流错误响应
     */
    public static final JingyuResponse<Object> RATE_LIMIT_ERROR = JingyuResponse.fail(429, "请求过于频繁，请稍后重试");

    /**
     * 熔断错误响应
     */
    public static final JingyuResponse<Object> CIRCUIT_BREAKER_ERROR = JingyuResponse.fail(503, "服务暂时不可用，请稍后重试");

    /**
     * 系统错误响应
     */
    public static final JingyuResponse<Object> SYSTEM_ERROR = JingyuResponse.fail(500, "系统内部错误");

    /**
     * 默认限流器名称
     */
    public static final String DEFAULT_RATE_LIMITER = "jingyuSend";

    /**
     * 默认熔断器名称
     */
    public static final String DEFAULT_CIRCUIT_BREAKER = "jingyuSend";
}
