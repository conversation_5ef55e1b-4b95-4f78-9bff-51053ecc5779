package com.leiting.ltvip.constant;

/**
 * 鲸域Resilience4j相关常量
 * <AUTHOR>
 * @date 2025/1/27
 */
public class JingyuResilienceConst {
    
    /**
     * 限流错误码
     */
    public static final int RATE_LIMIT_ERROR_CODE = 429;
    
    /**
     * 限流错误信息
     */
    public static final String RATE_LIMIT_ERROR_MESSAGE = "请求过于频繁，请稍后重试";
    
    /**
     * 熔断错误码
     */
    public static final int CIRCUIT_BREAKER_ERROR_CODE = 503;
    
    /**
     * 熔断错误信息
     */
    public static final String CIRCUIT_BREAKER_ERROR_MESSAGE = "服务暂时不可用，请稍后重试";
    
    /**
     * 系统错误码
     */
    public static final int SYSTEM_ERROR_CODE = 500;
    
    /**
     * 系统错误信息
     */
    public static final String SYSTEM_ERROR_MESSAGE = "系统内部错误";
    
    /**
     * 默认限流器名称
     */
    public static final String DEFAULT_RATE_LIMITER = "jingyuSend";
    
    /**
     * 默认熔断器名称
     */
    public static final String DEFAULT_CIRCUIT_BREAKER = "jingyuSend";
}
