[main]
config.managementUrl = ${manage.url}
config.appId = ltvip
config.manageKey = ${manage.key}
config.file = classpath:config.properties

[urls]
# 游戏配置-游戏信息配置
/admin/commonGameVipConfig/getPage=login,perms["commonGameVipConfigSelect/ltvip"]
/admin/commonGameVipConfig/add=login,perms["commonGameVipConfigAdd/ltvip"]
/admin/commonGameVipConfig/updateById=login,perms["commonGameVipConfigUpdate/ltvip"]
/admin/commonGameVipConfig/getGameMap=login
/admin/commonGameVipConfig/changeActiveStatus=login,perms["commonGameVipConfigUpdate/ltvip"]
/admin/commonGameVipConfig/synchronizedVipLevel=login,perms["commonGameVipConfigSyn/ltvip"]


# 游戏配置-特权配置
/admin/commonGameVipPrivilege/getPage=login,perms["commonGameVipPrivilegeSelect/ltvip"]
/admin/commonGameVipPrivilege/add=login,perms["commonGameVipPrivilegeAdd/ltvip"]
/admin/commonGameVipPrivilege/updateById=login,perms["commonGameVipPrivilegeUpdate/ltvip"]
/admin/commonGameVipPrivilege/getPrivilegeListForGame=login,perms["commonGameVipPrivilegeSelect/ltvip"]
/admin/commonGameVipPrivilege/getPrivilegeListForGameLevel=login,perms["commonGameVipPrivilegeSelect/ltvip"]
/admin/commonGameVipPrivilege/uploadImg=login,perms_any["commonGameVipPrivilegeAdd/ltvip","commonGameVipPrivilegeUpdate/ltvip"]

# 首页/雷霆VIP配置/游戏配置/会员等级配置(废弃)
/admin/commonVipLevelConfig/getReleaseConfigByGame=login,perms["commonVipLevelConfigSelect2/ltvip"]
/admin/commonVipLevelConfig/getPage=login,perms_any["commonVipLevelConfigSelect2/ltvip","commonVipLevelConfigSelect1/ltvip"]
/admin/commonVipLevelConfig/addNotReleaseEntity=login,perms["commonVipLevelConfigAdd/ltvip"]
/admin/commonVipLevelConfig/updateNotReleaseEntity=login,perms["commonVipLevelConfigUpdate/ltvip"]
/admin/commonVipLevelConfig/updateReleaseEntity=login,perms["commonVipLevelConfigUpdate/ltvip"]
/admin/commonVipLevelConfig/deleteNotReleaseById=login,perms["commonVipLevelConfigDalate/ltvip"]
/admin/commonVipLevelConfig/deleteReleaseById=login,perms["commonVipLevelConfigDalate/ltvip"]
/admin/commonVipLevelConfig/releaseLevelConfig=login,perms["commonVipLevelConfigRelease/ltvip"]
/admin/commonVipLevelConfig/getNeedReleaseConfigList=login,perms["commonVipLevelConfigRelease/ltvip"]


# 游戏配置-等级配置变更日志
/admin/levelConfigUpdateLog/getPage=login,perms["levelConfigUpdateLogSelect/ltvip"]


# 游戏配置-首页横幅配置
/admin/homePageBanner/getPage=login,perms["homePageBannerSelect/ltvip"]
/admin/homePageBanner/add=login,perms["homePageBannerAdd/ltvip"]
/admin/homePageBanner/updateById=login,perms["homePageBannerUpdate/ltvip"]
/admin/homePageBanner/deleteById=login,perms["homePageBannerDelete/ltvip"]
/admin/homePageBanner/changeShowStatus=login,perms["homePageBannerStatus/ltvip"]

# 游戏配置-游戏横幅配置
/admin/gamePageBanner/getPage=login,perms["gamePageBannerSelect/ltvip"]
/admin/gamePageBanner/add=login,perms["gamePageBannerAdd/ltvip"]
/admin/gamePageBanner/updateById=login,perms["gamePageBannerUpdate/ltvip"]
/admin/gamePageBanner/deleteById=login,perms["gamePageBannerDelete/ltvip"]

# 游戏配置-游戏角色绑定配置
/admin/roleBindConfig/getPage=login,perms["roleBindConfigSelect/ltvip"]
/admin/roleBindConfig/add=login,perms["roleBindConfigAdd/ltvip"]
/admin/roleBindConfig/updateById=login,perms["roleBindConfigUpdate/ltvip"]
/admin/roleBindConfig/deleteById=login,perms["roleBindConfigDelete/ltvip"]

# 游戏配置-会员充值门槛配置
/admin/vipChargeLimit/getPage=login,perms["vipChargeLimitSelect/ltvip"]
/admin/vipChargeLimit/addNotReleaseEntity=login,perms["vipChargeLimitAdd/ltvip"]
/admin/vipChargeLimit/updateNotReleaseEntity=login,perms["vipChargeLimitUpdate/ltvip"]
/admin/vipChargeLimit/updateReleaseEntity=login,perms["vipChargeLimitUpdate/ltvip"]
/admin/vipChargeLimit/deleteNotReleaseById=login,perms["vipChargeLimitDelete/ltvip"]
/admin/vipChargeLimit/releaseLevelConfig=login,perms["vipChargeLimitDeleteRelease/ltvip"]
/admin/vipChargeLimit/getNeedReleaseConfig=login

# 游戏配置-积分会员等级配置
/admin/creditVipLevelConfig/getPage=login,perms["creditVipLevelConfigSelect/ltvip"]
/admin/creditVipLevelConfig/addNotReleaseEntity=login,perms["creditVipLevelConfigAdd/ltvip"]
/admin/creditVipLevelConfig/updateNotReleaseEntity=login,perms["creditVipLevelConfigUpdate/ltvip"]
/admin/creditVipLevelConfig/updateReleaseEntity=login,perms["creditVipLevelConfigUpdate/ltvip"]
/admin/creditVipLevelConfig/deleteNotReleaseById=login,perms["creditVipLevelConfigDalate/ltvip"]
/admin/creditVipLevelConfig/deleteReleaseById=login,perms["creditVipLevelConfigDalate/ltvip"]
/admin/creditVipLevelConfig/releaseLevelConfig=login,perms["creditVipLevelConfigRelease/ltvip"]
/admin/creditVipLevelConfig/getNeedReleaseConfigList=login
/admin/creditVipLevelConfig/getReleaseConfigByGame=login


# 游戏配置-游戏邮件模板配置
/admin/gameEmailTemplate/getPage=login,perms["gameEmailConfigSelect/ltvip"]
/admin/gameEmailTemplate/add=login,perms["gameEmailConfigAdd/ltvip"]
/admin/gameEmailTemplate/updateById=login,perms["gameEmailConfigUpdate/ltvip"]
/admin/gameEmailTemplate/deleteById=login,perms["gameEmailConfigDelete/ltvip"]



# 游戏配置-游戏活动横幅配置
/admin/gameActivityBanner/getPage=login,perms["gameBannerConfigSelect/ltvip"]
/admin/gameActivityBanner/add=login,perms["gameBannerConfigAdd/ltvip"]
/admin/gameActivityBanner/updateById=login,perms["gameBannerConfigUpdate/ltvip"]
/admin/gameActivityBanner/deleteById=login,perms["gameBannerConfigDelete/ltvip"]

# 查证日志-会员等级变更日志
/admin/commonLevelUpdateLog/getPage=login,perms["levelUpdateLogSelect/ltvip"]

# 查证日志-会员资格操作日志
/admin/vipQualificationActionLog/getPage=login,perms["vipQualificationActionLogSelect/ltvip"]

# 查证日志-等级变更统计日
/admin/chargeStatisticLog/getPage=login,perms["chargeStatisticLogSelect/ltvip"]
/admin/chargeStatisticLog/notifyRetry=login,perms["chargeStatisticLogUpdate/ltvip"]

# 查证日志-生日礼包发送记录
/admin/birthGiftLog/getPage=login,perms["birthGiftLogSelect/ltvip"]
# 目前前端没有使用该接口，birthGiftLogManualPush是新加的权限
/admin/birthGiftLog/manualPush=login,perms["birthGiftLogManualPush/ltvip"]

# 查证日志-礼包领取记录
/admin/giftDrawLog/getPage=login,perms["giftDrawLogSelect/ltvip"]
/admin/giftDrawLog/export=login,perms["giftDrawLogSelect/ltvip"]

# 查证日志-用户积分流水记录
/admin/creditRecord/getPage=login,perms["creditRecordSelect/ltvip"]

# 查证日志-积分礼包领取记录
/admin/creditGiftDrawLog/getPage=login,perms["creditGiftDrawLogSelect/ltvip"]
/admin/creditGiftDrawLog/manualPush=login,perms["creditGiftDrawLogRethrust/ltvip"]

# 查证日志-抽奖记录日志
/admin/creditGiftDrawLog/getLotteryPage=login,perms["lotteryLogSelect/ltvip"]


# 会员配置-会员信息
/admin/vipUserInfo/getPage=login,perms["vipUserInfoSelect/ltvip"]
/admin/vipUserInfo/synchronizedUserInfo=login,perms["vipUserInfoSyn/ltvip"]
/admin/vipUserInfo/importVipUserBatch=login,perms["vipUserInfoAdd/ltvip"]
/admin/vipUserInfo/cancelVipUserBatch=login,perms["vipUserInfoCancel/ltvip"]
/admin/vipUserInfo/cancelVipUser=login,perms["vipUserInfoCancel/ltvip"]
/admin/vipUserInfo/activeVipUser=login,perms["vipUserInfoUpdate/ltvip"]
/admin/vipUserInfo/addVipUser=login,perms["vipUserInfoAdd/ltvip"]
/admin/vipUserInfo/editVipInfo=login,perms["vipUserInfoUpdate/ltvip"]
/admin/vipUserInfo/resetWxBinding=login,perms["vipUserInfoEesetBind/ltvip"]
/admin/vipUserInfo/importUserWxAccount=login,perms["vipUserInfoAdd/ltvip"]
/admin/vipUserInfo/createUserInfoCsv=login,perms["vipUserInfoExport/ltvip"]



# 会员配置-维系人配置
/admin/assistant/getPage=login,perms["assistantSelect/ltvip"]
/admin/assistant/add=login,perms["assistantAdd/ltvip"]
/admin/assistant/updateById=login,perms["assistantUpdate/ltvip"]
/admin/assistant/deleteById=login,perms["assistantDelete/ltvip"]
/admin/assistant/getAssistantList=login


# 会员配置-会员积分信息
/admin/userCredit/getPage=login,perms["vipUserCreditSelect/ltvip"]
/admin/userCredit/addCreditForUser=login,perms["vipUserCreditAdd/ltvip"]
/admin/userCredit/reduceCreditForUser=login,perms["vipUserCreditAdd/ltvip"]


# 会员配置-积分翻倍配置
/admin/creditAwardConfig/getPage=login,perms["creditAwardConfigSelect/ltvip"]
/admin/creditAwardConfig/add=login,perms["creditAwardConfigAdd/ltvip"]
/admin/creditAwardConfig/updateById=login,perms["creditAwardConfigUpdate/ltvip"]


# 微信配置-微信APP信息
/admin/wxAppInfo/getPage=login,perms["wxAppSelect/ltvip"]
/admin/wxAppInfo/add=login,perms["wxAppAdd/ltvip"]
/admin/wxAppInfo/updateById=login,perms["wxAppUpdate/ltvip"]
/admin/wxAppInfo/getByAppSign=login

# 微信配置-微信用户信息
/admin/wxUserInfo/getPage=login,perms["wxUserInfoSelect/ltvip"]


# 智齿-智齿配置
/admin/toothTag/getPage=login,perms["toothTagSelect/ltvip"]
/admin/toothTag/add=login,perms["toothTagAdd/ltvip"]
/admin/toothTag/updateById=login,perms["toothTagUpdate/ltvip"]
/admin/toothTag/deleteById=login,perms["toothTagDelete/ltvip"]


# 智齿-智齿会员绑定
/admin/toothBind/getPage=login,perms["toothMembersSelect/ltvip"]
/admin/toothBind/addBindVip=login,perms["toothMembersAdd/ltvip"]


# 智齿-自定义玩家标签配置
/admin/toothCustomTagConfig/getPage=login,perms["TOOTHTAGCONFIG_QUERY/ltvip"]
/admin/toothCustomTagConfig/add=login,perms["TOOTHTAGCONFIG_ADD/ltvip"]
/admin/toothCustomTagConfig/updateById=login,perms["TOOTHTAGCONFIG_UPDATE/ltvip"]
/admin/toothCustomTagConfig/deleteById=login,perms["TOOTHTAGCONFIG_DEL/ltvip"]
/admin/toothCustomTagConfig/getBindGameList=login


# 智齿-智齿玩家标签记录
/admin/toothCommonLog/getPage=login,perms["TOOTHCOMMONLOG_QUERY/ltvip"]
/admin/toothCommonLog/unbindWx=login,perms["TOOTHCOMMONLOG_UNBINDWX/ltvip"]

# 智齿-智齿游戏配置
/admin/toothBindGame/getPage=login,perms["toothConfigSelect/ltvip"]
/admin/toothBindGame/add=login,perms["toothConfigAdd/ltvip"]
/admin/toothBindGame/update=login,perms["toothConfigUpdate/ltvip"]
/admin/toothBindGame/gameList=login

# 礼包配置-会员礼包配置
/admin/vipGiftConfig/getPage=login,perms["vipGiftConfigSelect/ltvip"]
/admin/vipGiftConfig/add=login,perms["vipGiftConfigAdd/ltvip"]
/admin/vipGiftConfig/updateById=login,perms["vipGiftConfigUpdate/ltvip"]
/admin/vipGiftConfig/deleteById=login,perms["vipGiftConfigDelete/ltvip"]

# 礼包配置-积分礼包配置
/admin/creditGiftConfig/getPage=login,perms["creditGiftConfigSelect/ltvip"]
/admin/creditGiftConfig/addEntity=login,perms["creditGiftConfigAdd/ltvip"]
/admin/creditGiftConfig/updateEntityById=login,perms["creditGiftConfigUpdate/ltvip"]
/admin/creditGiftConfig/deleteById=login,perms["creditGiftConfigDelete/ltvip"]
/admin/creditGiftConfig/getActiveConfigList=login

# 积分商城-用户实物订单信息
/admin/goodsOrder/getPage=login,perms["goodsOrderSelect/ltvip"]
/admin/goodsOrder/importExpressInfo=login,perms["goodsOrderImport/ltvip"]


# 积分商城-用户快递信息
/admin/userExpressAddress/getPage=login,perms["userExpressAddressSelect/ltvip"]

# 文件生成记录
/admin/fileGenerateRecord/getPage=login,perms["vipFileGenerateRecordSelect/ltvip"]
/admin/fileGenerateRecord/checkFileExist=login
/admin/fileGenerateRecord/downloadFile=login,perms["vipFileGenerateRecordDown/ltvip"]

# 小程序配置-轮播图配置
/admin/carouselPictureConfig/getPage=login,perms["CAROUSELPICCFG_QUERY/ltvip"]
/admin/carouselPictureConfig/add=login,perms["CAROUSELPICCFG_ADD/ltvip"]
/admin/carouselPictureConfig/updateById=login,perms["CAROUSELPICCFG_EDIT/ltvip"]
/admin/carouselPictureConfig/deleteById=login,perms["CAROUSELPICCFG_DEL/ltvip"]

# 小程序配置-轮播信息配置
/admin/carouselMessageConfig/getPage=login,perms["CAROUSELINFOCFG_QUERY/ltvip"]
/admin/carouselMessageConfig/add=login,perms["CAROUSELINFOCFG_ADD/ltvip"]
/admin/carouselMessageConfig/updateById=login,perms["CAROUSELINFOCFG_EDIT/ltvip"]
/admin/carouselMessageConfig/deleteById=login,perms["CAROUSELINFOCFG_DEL/ltvip"]


# 小程序配置-新闻配置
/admin/newsConfig/getPage=login,perms["NEWSCONFIG_QUERY/ltvip"]
/admin/newsConfig/add=login,perms["NEWSCONFIG_ADD/ltvip"]
/admin/newsConfig/updateById=login,perms["NEWSCONFIG_EDIT/ltvip"]
/admin/newsConfig/deleteById=login,perms["NEWSCONFIG_DEL/ltvip"]


# 小程序配置-福利活动配置
/admin/superiorActivity/getPage=login,perms["WELFAREACTCFG_QUERY/ltvip"]
/admin/superiorActivity/add=login,perms["WELFAREACTCFG_ADD/ltvip"]
/admin/superiorActivity/updateById=login,perms["WELFAREACTCFG_EDIT/ltvip"]
/admin/superiorActivity/copyConfig=login,perms["WELFAREACTCFG_GIFT/ltvip"]

/admin/superiorActivityGift/getPage=login,perms["WELFAREACTCFG_GIFT_QUERY/ltvip"]
/admin/superiorActivityGift/add=login,perms["WELFAREACTCFG_GIFT_ADD/ltvip"]
/admin/superiorActivityGift/updateById=login,perms["WELFAREACTCFG_GIFT_EDIT/ltvip"]
/admin/superiorActivityGift/addSkuById=login,perms["WELFAREACTCFG_GIFT_ADD/ltvip"]


# 小程序配置-订阅消息配置
/admin/subscribeMsgConfig/getPage=login,perms["SUBSCRIBECFG_QUERY/ltvip"]
/admin/subscribeMsgConfig/add=login,perms["SUBSCRIBECFG_ADD/ltvip"]
/admin/subscribeMsgConfig/updateById=login,perms["SUBSCRIBECFG_EDIT/ltvip"]
/admin/subscribeMsgConfig/deleteById=login,perms["SUBSCRIBECFG_DEL/ltvip"]


# 小程序配置-推荐礼包配置
/admin/giftRecommend/getPage=login,perms["GIFTRECOMMEND_QUERY/ltvip"]
/admin/giftRecommend/add=login,perms["GIFTRECOMMEND_ADD/ltvip"]
/admin/giftRecommend/updateById=login,perms["GIFTRECOMMEND_UPDATE/ltvip"]
/admin/giftRecommend/deleteById=login,perms["GIFTRECOMMEND_DEL/ltvip"]

# 小程序配置-身份认证白名单
/admin/vipIdentityWhiteConfig/getPage=login,perms["VIPIDENTITYWHITECFG_QUERY/ltvip"]
/admin/vipIdentityWhiteConfig/add=login,perms["VIPIDENTITYWHITECFG_ADD/ltvip"]
/admin/vipIdentityWhiteConfig/batchAdd=login,perms["VIPIDENTITYWHITECFG_BATCH_ADD/ltvip"]
/admin/vipIdentityWhiteConfig/updateById=login,perms["VIPIDENTITYWHITECFG_UPDATE/ltvip"]
/admin/vipIdentityWhiteConfig/deleteById=login,perms["VIPIDENTITYWHITECFG_DEL/ltvip"]
/admin/vipIdentityWhiteConfig/batchDeleteById=login,perms["VIPIDENTITYWHITECFG_BATCH_DEL/ltvip"]

# 小程序配置-规则配置
/admin/ruleConfig/getPage=login,perms["RULECONFIG_QUERY/ltvip"]
/admin/ruleConfig/add=login,perms["RULECONFIG_ADD/ltvip"]
/admin/ruleConfig/updateById=login,perms["RULECONFIG_EDIT/ltvip"]
/admin/ruleConfig/deleteById=login,perms["RULECONFIG_DEL/ltvip"]

# AI知识库-FAQ
/admin/ai_knowledge_faq/get_page=login,perms["ai_knowledge_faq_select"]
/admin/ai_knowledge_faq/add=login,perms["ai_knowledge_faq_add"]
/admin/ai_knowledge_faq/delete=login,perms["ai_knowledge_faq_delete"]
/admin/ai_knowledge_faq/modify=login,perms["ai_knowledge_faq_update"]
/admin/ai_knowledge_faq/import_file=login,perms["ai_knowledge_faq_import"]

# AI知识库-游戏黑话
/admin/ai_knowledge_nouns/get_page=login,perms["ai_knowledge_nouns_select"]
/admin/ai_knowledge_nouns/add=login,perms["ai_knowledge_nouns_add"]
/admin/ai_knowledge_nouns/delete=login,perms["ai_knowledge_nouns_delete"]
/admin/ai_knowledge_nouns/modify=login,perms["ai_knowledge_nouns_update"]
/admin/ai_knowledge_nouns/import_file=login,perms["ai_knowledge_nouns_import"]

# 授权登录
/admin/auth_login/get_auth_list=login,perms["AUTHLOGIN_QUERY"]
/admin/auth_login/get_auth_detail=login,perms["AUTHLOGIN_DETAIL"]
/admin/auth_login/cancel_auth=login,perms["AUTHLOGIN_CANCEL_AUTH"]
/admin/auth_login/clear_create_num=login,perms["AUTHLOGIN_CLEAR_CREAT_NUM"]
/admin/auth_login/clear_login_fail_num=login,perms["AUTHLOGIN_CLEAR_LOGIN_FAIL_NUM"]

/admin/** = login
/admin/db-query = login,perms["ROLE_DB_QUERY_SEARCH"]
/swagger-ui.html=login
/v2/api-docs=login
