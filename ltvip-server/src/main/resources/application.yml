##
debug: false
server:
  port: 8084

# C3P0数据源配置
sharding.jdbc.datasource.names: ds1-m,ds1-s
sharding.jdbc.datasource.ds1-m.type: com.mchange.v2.c3p0.ComboPooledDataSource
sharding.jdbc.datasource.ds1-m.driver-class: com.mysql.jdbc.Driver
sharding.jdbc.datasource.ds1-m.jdbc-url: ${project.url}
sharding.jdbc.datasource.ds1-m.user: ${project.username}
sharding.jdbc.datasource.ds1-m.password: ${project.password}
sharding.jdbc.datasource.ds1-m.initial-pool-size: 10
sharding.jdbc.datasource.ds1-m.min-pool-size: 10
sharding.jdbc.datasource.ds1-m.max-pool-size: 50
sharding.jdbc.datasource.ds1-m.max-idle-time: 7200
sharding.jdbc.datasource.ds1-m.acquire-increment: 5
sharding.jdbc.datasource.ds1-m.max-statements: 1000
sharding.jdbc.datasource.ds1-m.idle-connection-test-period: 60
sharding.jdbc.datasource.ds1-m.acquire-retry-attempts: 30
sharding.jdbc.datasource.ds1-m.acquire-retry-delay: 1000
sharding.jdbc.datasource.ds1-m.break-after-acquire-failure: false
sharding.jdbc.datasource.ds1-m.test-connection-on-checkout: false

sharding.jdbc.datasource.ds1-s.type: com.mchange.v2.c3p0.ComboPooledDataSource
sharding.jdbc.datasource.ds1-s.driver-class: com.mysql.jdbc.Driver
sharding.jdbc.datasource.ds1-s.jdbc-url: ${read.url}
sharding.jdbc.datasource.ds1-s.user: ${read.username}
sharding.jdbc.datasource.ds1-s.password: ${read.password}
sharding.jdbc.datasource.ds1-s.initial-pool-size: 10
sharding.jdbc.datasource.ds1-s.min-pool-size: 10
sharding.jdbc.datasource.ds1-s.max-pool-size: 50
sharding.jdbc.datasource.ds1-s.max-idle-time: 7200
sharding.jdbc.datasource.ds1-s.acquire-increment: 5
sharding.jdbc.datasource.ds1-s.max-statements: 1000
sharding.jdbc.datasource.ds1-s.idle-connection-test-period: 60
sharding.jdbc.datasource.ds1-s.acquire-retry-attempts: 30
sharding.jdbc.datasource.ds1-s.acquire-retry-delay: 1000
sharding.jdbc.datasource.ds1-s.break-after-acquire-failure: false
sharding.jdbc.datasource.ds1-s.test-connection-on-checkout: false

sharding:
  jdbc:
    config:
      sharding:
        master-slave-rules:
          ds1:
            masterDataSourceName: ds1-m
            slaveDataSourceNames:
              - ds1-s
        default-data-source-name: ds1
        default-database-strategy:
          hint:
            algorithm-class-name: com.leiting.ltvip.config.datasource.MyDatasourceRoutingAlgorithm

mybatis:
  configuration:
    map-underscore-to-camel-case: true
  mapper-locations: classpath*:com/leiting/ltvip/mapper/*DB/xml/*Mapper.xml

spring:
  redis:
    database: 3
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.pass}
    timeout: 5000 # 连接超时时间 单位 ms（毫秒）
    pool:
      max-idle: 8 # 连接池中的最大空闲连接，默认值也是8。
      min-idle: 0 #连接池中的最小空闲连接，默认值也是0。
      max-active: 8 # 如果赋值为-1，则表示不限制；如果pool已经分配了maxActive个jedis实例，则此时pool的状态为exhausted(耗尽)。
      max-wait: 10000 # 等待可用连接的最大时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
  cache:
    type: redis

project:
  name: ltvip
  session-user-name: auth_user
  session:
    enable: true  #是否开启redis session 默认false
    redis-session-prefix: ltvip:session  #存放redis的key，默认leiting:session 一般配置项目名:session
    ignore: /g/api/*,/om/*,/front/*   #不需要session的接口配置，多个逗号隔开。
    timeout: 2880  #session过期时间，默认30分钟
    cookie-domain:  #默认会根据请求地址设置，一般不需要配置，如果项目代码中写死了cookie-domain则在此配置。
    session-id: ltvip-session-id #session-id配置，默认sid
  config: classpath:database.properties   # 可替换的外部配置，不要在代码中使用，只在application.yml中使用

management:
  endpoints:
    enabled-by-default: false
    web:
      exposure.include: "*"

dev: ${dev}
env: ${env}

log.level: ${log.level}
show.sql.log.level: ${show.sql.log.level}

request:
  ip:
    # -n表示取倒数第n个，以实际线上情况为准
    pattern: X-Forwarded-For[-1]

# Resilience4j配置 - 鲸域API流量整型和熔断
resilience4j:
  ratelimiter:
    instances:
      jingyuSend:
        limit-for-period: 10 # 每个周期允许的请求数
        limit-refresh-period: 1s # 刷新周期
        timeout-duration: 3s # 等待许可的超时时间
  circuitbreaker:
    instances:
      jingyuSend:
        failure-rate-threshold: 50 # 失败率阈值50%
        slow-call-rate-threshold: 80 # 慢调用率阈值80%
        slow-call-duration-threshold: 5s # 慢调用时间阈值
        wait-duration-in-open-state: 30s # 熔断器打开状态持续时间
        sliding-window-size: 20 # 滑动窗口大小
        minimum-number-of-calls: 10 # 最小调用次数
        permitted-number-of-calls-in-half-open-state: 5 # 半开状态允许的调用次数
        automatic-transition-from-open-to-half-open-enabled: true

# Redis配置 - 支持分布式限流和熔断
spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: 3000ms
    jedis:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

---
# 默认配置
spring:
  profiles: default
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

---
# 开发配置
spring:
  profiles: dev
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
---
# 线上配置
spring:
  profiles: prod
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
