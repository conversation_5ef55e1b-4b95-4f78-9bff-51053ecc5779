# 每个接入AI知识库的游戏项目都需要初始化以下2张表
CREATE TABLE `{gameCode}_ai_knowledge_faq`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_date` datetime            NOT NULL COMMENT '创建时间',
    `modify_date` datetime            NOT NULL COMMENT '修改时间',
    `record_id`   varchar(64)         NOT NULL COMMENT '记录ID',
    `question`    varchar(1024)       NOT NULL COMMENT '问题',
    `similar`     text        DEFAULT NULL COMMENT '相似问',
    `answer`      mediumtext          NOT NULL COMMENT '回答',
    `operator`    varchar(32) DEFAULT NULL COMMENT '操作人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_unique_record_id` (`record_id`),
    KEY `idx_create_date` (`create_date`),
    KEY `idx_modify_date` (`modify_date`),
    KEY `idx_operator` (`operator`),
    FULLTEXT KEY `idx_question_similar_answer` (`question`, `similar`, `answer`) with parser ngram COMMENT '联合全文索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='AI知识库-FAQ';

CREATE TABLE `{gameCode}_ai_knowledge_nouns`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_date` datetime            NOT NULL COMMENT '创建时间',
    `modify_date` datetime            NOT NULL COMMENT '修改时间',
    `record_id`   varchar(64)         NOT NULL COMMENT '记录ID',
    `nouns`       varchar(1024)        NOT NULL COMMENT '黑话',
    `explanation` text                NOT NULL COMMENT '解释',
    `operator`    varchar(32) DEFAULT NULL COMMENT '操作人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_unique_record_id` (`record_id`),
    KEY `idx_create_date` (`create_date`),
    KEY `idx_modify_date` (`modify_date`),
    KEY `idx_operator` (`operator`),
    FULLTEXT KEY `idx_nouns_explanation` (`nouns`, `explanation`) with parser ngram COMMENT '联合全文索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='AI知识库-游戏黑话';