package com.leiting.ltvip.config;

import org.redisson.api.RedissonClient;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;

/**
 * 测试配置类
 * 避免多个@SpringBootConfiguration冲突
 * <AUTHOR>
 * @date 2025/1/27
 */
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScan(basePackages = {
    "com.leiting.ltvip.service",
    "com.leiting.ltvip.aspect",
    "com.leiting.ltvip.resilience",
    "com.leiting.ltvip.config"
})
public class TestConfiguration {

    @MockBean
    private RedissonClient redissonClient;
}
