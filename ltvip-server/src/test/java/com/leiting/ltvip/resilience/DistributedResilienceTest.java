package com.leiting.ltvip.resilience;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Duration;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 分布式限流和熔断单元测试
 * <AUTHOR>
 * @date 2025/1/27
 */
@RunWith(MockitoJUnitRunner.class)
public class DistributedResilienceTest {
    
    private static final Logger logger = LoggerFactory.getLogger(DistributedResilienceTest.class);
    
    @Mock
    private RedissonClient redissonClient;
    
    @Mock
    private RRateLimiter rateLimiter;
    
    @Mock
    private RBucket<String> bucket;
    
    @Mock
    private RAtomicLong atomicLong;
    
    private DistributedRateLimiter distributedRateLimiter;
    private DistributedCircuitBreaker distributedCircuitBreaker;
    
    @Before
    public void setUp() {
        distributedRateLimiter = new DistributedRateLimiter();
        distributedCircuitBreaker = new DistributedCircuitBreaker();
        
        // 注入Mock对象
        ReflectionTestUtils.setField(distributedRateLimiter, "redissonClient", redissonClient);
        ReflectionTestUtils.setField(distributedCircuitBreaker, "redissonClient", redissonClient);
    }
    
    @Test
    public void testRateLimiterTryAcquire_Success() {
        logger.info("测试分布式限流器 - 获取许可成功");
        
        // Mock设置
        when(redissonClient.getRateLimiter(anyString())).thenReturn(rateLimiter);
        when(rateLimiter.isExists()).thenReturn(true);
        when(rateLimiter.tryAcquire(anyInt(), anyLong(), any())).thenReturn(true);
        
        // 执行测试
        boolean result = distributedRateLimiter.tryAcquire("test", 1, 10, 1, Duration.ofSeconds(3));
        
        // 验证结果
        assertTrue("应该成功获取许可", result);
        verify(rateLimiter).tryAcquire(1, 3000L, java.util.concurrent.TimeUnit.MILLISECONDS);
    }
    
    @Test
    public void testRateLimiterTryAcquire_Failed() {
        logger.info("测试分布式限流器 - 获取许可失败");
        
        // Mock设置
        when(redissonClient.getRateLimiter(anyString())).thenReturn(rateLimiter);
        when(rateLimiter.isExists()).thenReturn(true);
        when(rateLimiter.tryAcquire(anyInt(), anyLong(), any())).thenReturn(false);
        
        // 执行测试
        boolean result = distributedRateLimiter.tryAcquire("test", 1, 10, 1, Duration.ofSeconds(3));
        
        // 验证结果
        assertFalse("应该获取许可失败", result);
    }
    
    @Test
    public void testRateLimiterGetStatus() {
        logger.info("测试分布式限流器 - 获取状态");
        
        // Mock设置
        when(redissonClient.getRateLimiter(anyString())).thenReturn(rateLimiter);
        when(rateLimiter.isExists()).thenReturn(true);
        when(rateLimiter.availablePermits()).thenReturn(5L);
        
        // 执行测试
        DistributedRateLimiter.RateLimiterStatus status = distributedRateLimiter.getStatus("test");
        
        // 验证结果
        assertNotNull("状态不应为空", status);
        assertEquals("名称应该匹配", "test", status.getName());
        assertTrue("应该存在", status.isExists());
        assertEquals("可用许可应该为5", 5L, status.getAvailablePermits());
    }
    
    @Test
    public void testCircuitBreakerIsCallPermitted_Closed() {
        logger.info("测试分布式熔断器 - CLOSED状态允许调用");
        
        // Mock设置
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(bucket.get()).thenReturn("CLOSED");
        
        // 执行测试
        boolean result = distributedCircuitBreaker.isCallPermitted("test", 50, 10, Duration.ofSeconds(30));
        
        // 验证结果
        assertTrue("CLOSED状态应该允许调用", result);
    }
    
    @Test
    public void testCircuitBreakerIsCallPermitted_Open() {
        logger.info("测试分布式熔断器 - OPEN状态拒绝调用");
        
        // Mock设置
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(bucket.get()).thenReturn("OPEN");
        
        // 执行测试
        boolean result = distributedCircuitBreaker.isCallPermitted("test", 50, 10, Duration.ofSeconds(30));
        
        // 验证结果
        assertFalse("OPEN状态应该拒绝调用", result);
    }
    
    @Test
    public void testCircuitBreakerRecordSuccess() {
        logger.info("测试分布式熔断器 - 记录成功");
        
        // Mock设置
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(redissonClient.getAtomicLong(anyString())).thenReturn(atomicLong);
        when(bucket.get()).thenReturn("CLOSED");
        when(atomicLong.incrementAndGet()).thenReturn(1L);
        
        // 执行测试
        distributedCircuitBreaker.recordSuccess("test");
        
        // 验证调用
        verify(atomicLong).incrementAndGet();
        verify(atomicLong).expire(300, java.util.concurrent.TimeUnit.SECONDS);
    }
    
    @Test
    public void testCircuitBreakerRecordFailure() {
        logger.info("测试分布式熔断器 - 记录失败");
        
        // Mock设置
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(redissonClient.getAtomicLong(anyString())).thenReturn(atomicLong);
        when(atomicLong.incrementAndGet()).thenReturn(1L);
        when(atomicLong.get()).thenReturn(5L); // 失败次数
        
        // 执行测试
        distributedCircuitBreaker.recordFailure("test", 50, 10);
        
        // 验证调用
        verify(atomicLong).incrementAndGet();
        verify(atomicLong).expire(300, java.util.concurrent.TimeUnit.SECONDS);
    }
    
    @Test
    public void testCircuitBreakerGetStatus() {
        logger.info("测试分布式熔断器 - 获取状态");
        
        // Mock设置
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(redissonClient.getAtomicLong(anyString())).thenReturn(atomicLong);
        when(bucket.get()).thenReturn("CLOSED");
        when(atomicLong.get()).thenReturn(10L, 2L); // 成功10次，失败2次
        
        // 执行测试
        DistributedCircuitBreaker.CircuitBreakerStatus status = distributedCircuitBreaker.getStatus("test");
        
        // 验证结果
        assertNotNull("状态不应为空", status);
        assertEquals("名称应该匹配", "test", status.getName());
        assertEquals("状态应该为CLOSED", DistributedCircuitBreaker.State.CLOSED, status.getState());
        assertEquals("成功次数应该为10", 10L, status.getSuccessCount());
        assertEquals("失败次数应该为2", 2L, status.getFailureCount());
    }
    
    @Test
    public void testExceptionHandling() {
        logger.info("测试异常处理 - 应该允许通过");
        
        // Mock设置 - 抛出异常
        when(redissonClient.getRateLimiter(anyString())).thenThrow(new RuntimeException("Redis连接异常"));
        
        // 执行测试
        boolean result = distributedRateLimiter.tryAcquire("test", 1, 10, 1, Duration.ofSeconds(3));
        
        // 验证结果 - 异常情况下应该允许通过
        assertTrue("异常情况下应该允许通过", result);
    }
}
