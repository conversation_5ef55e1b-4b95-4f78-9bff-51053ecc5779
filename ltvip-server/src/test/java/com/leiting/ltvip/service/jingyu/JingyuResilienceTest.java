package com.leiting.ltvip.service.jingyu;

import com.leiting.ltvip.constant.JingyuURI;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 鲸域Resilience4j功能测试
 * <AUTHOR>
 * @date 2025/1/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class JingyuResilienceTest {
    
    private static final Logger logger = LoggerFactory.getLogger(JingyuResilienceTest.class);
    
    @Autowired
    private JingyuSendService jingyuSendService;
    
    @Autowired
    private JingyuResilienceMonitorService monitorService;
    
    /**
     * 测试限流功能
     * 快速发送多个请求，验证限流器是否正常工作
     */
    @Test
    public void testRateLimiting() {
        logger.info("开始测试鲸域限流功能");
        
        // 打印初始状态
        Map<String, Object> initialStatus = monitorService.getSendRateLimiterStatus();
        logger.info("初始限流器状态: {}", initialStatus);
        
        ExecutorService executor = Executors.newFixedThreadPool(20);
        
        // 并发发送20个请求，超过限流器配置的每秒10个请求
        for (int i = 0; i < 20; i++) {
            final int requestId = i;
            CompletableFuture.runAsync(() -> {
                try {
                    String testParams = "{\"test\": \"request_" + requestId + "\"}";
                    String response = jingyuSendService.request(JingyuURI.SEND_MSG_TO_GROUP, testParams);
                    logger.info("请求 {} 响应: {}", requestId, response);
                } catch (Exception e) {
                    logger.error("请求 {} 异常", requestId, e);
                }
            }, executor);
        }
        
        // 等待所有请求完成
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 打印最终状态
        Map<String, Object> finalStatus = monitorService.getSendRateLimiterStatus();
        logger.info("最终限流器状态: {}", finalStatus);
        
        executor.shutdown();
        logger.info("限流功能测试完成");
    }
    
    /**
     * 测试熔断功能
     * 模拟服务异常，验证熔断器是否正常工作
     */
    @Test
    public void testCircuitBreaker() {
        logger.info("开始测试鲸域熔断功能");
        
        // 打印初始状态
        Map<String, Object> initialStatus = monitorService.getSendCircuitBreakerStatus();
        logger.info("初始熔断器状态: {}", initialStatus);
        
        // 发送一些正常请求
        for (int i = 0; i < 5; i++) {
            try {
                String testParams = "{\"test\": \"normal_request_" + i + "\"}";
                String response = jingyuSendService.request(JingyuURI.SEND_MSG_TO_GROUP, testParams);
                logger.info("正常请求 {} 响应: {}", i, response);
                Thread.sleep(100);
            } catch (Exception e) {
                logger.error("正常请求 {} 异常", i, e);
            }
        }
        
        // 模拟异常请求（通过发送无效参数）
        for (int i = 0; i < 15; i++) {
            try {
                String invalidParams = "invalid_json_" + i;
                String response = jingyuSendService.request(JingyuURI.SEND_MSG_TO_GROUP, invalidParams);
                logger.info("异常请求 {} 响应: {}", i, response);
                Thread.sleep(100);
            } catch (Exception e) {
                logger.error("异常请求 {} 异常", i, e);
            }
        }
        
        // 打印中间状态
        Map<String, Object> middleStatus = monitorService.getSendCircuitBreakerStatus();
        logger.info("中间熔断器状态: {}", middleStatus);
        
        // 继续发送请求，验证熔断器是否打开
        for (int i = 0; i < 5; i++) {
            try {
                String testParams = "{\"test\": \"after_circuit_open_" + i + "\"}";
                String response = jingyuSendService.request(JingyuURI.SEND_MSG_TO_GROUP, testParams);
                logger.info("熔断后请求 {} 响应: {}", i, response);
                Thread.sleep(100);
            } catch (Exception e) {
                logger.error("熔断后请求 {} 异常", i, e);
            }
        }
        
        // 打印最终状态
        Map<String, Object> finalStatus = monitorService.getSendCircuitBreakerStatus();
        logger.info("最终熔断器状态: {}", finalStatus);
        
        logger.info("熔断功能测试完成");
    }
    
    /**
     * 测试监控功能
     */
    @Test
    public void testMonitoring() {
        logger.info("开始测试鲸域监控功能");
        
        // 获取所有状态
        Map<String, Object> allStatus = monitorService.getAllStatus();
        logger.info("所有监控状态: {}", allStatus);
        
        // 发送一些请求
        for (int i = 0; i < 3; i++) {
            try {
                String testParams = "{\"test\": \"monitor_test_" + i + "\"}";
                String response = jingyuSendService.request(JingyuURI.SEND_MSG_TO_GROUP, testParams);
                logger.info("监控测试请求 {} 响应: {}", i, response);
                Thread.sleep(500);
            } catch (Exception e) {
                logger.error("监控测试请求 {} 异常", i, e);
            }
        }
        
        // 再次获取状态
        Map<String, Object> updatedStatus = monitorService.getAllStatus();
        logger.info("更新后监控状态: {}", updatedStatus);
        
        logger.info("监控功能测试完成");
    }
    
    /**
     * 测试认证接口的Resilience4j功能
     */
    @Test
    public void testAuthResilience() {
        logger.info("开始测试鲸域认证接口Resilience4j功能");
        
        // 打印初始状态
        Map<String, Object> authRateLimiterStatus = monitorService.getAuthRateLimiterStatus();
        Map<String, Object> authCircuitBreakerStatus = monitorService.getAuthCircuitBreakerStatus();
        logger.info("认证限流器初始状态: {}", authRateLimiterStatus);
        logger.info("认证熔断器初始状态: {}", authCircuitBreakerStatus);
        
        // 通过JingyuAccessService间接测试认证接口
        // 注意：这里需要确保有有效的配置才能真正测试
        logger.info("认证接口Resilience4j功能测试完成（需要有效配置才能完整测试）");
    }
    
    /**
     * 压力测试
     * 模拟高并发场景，验证系统稳定性
     */
    @Test
    public void testHighConcurrency() {
        logger.info("开始鲸域高并发压力测试");
        
        ExecutorService executor = Executors.newFixedThreadPool(50);
        
        // 并发发送100个请求
        for (int i = 0; i < 100; i++) {
            final int requestId = i;
            CompletableFuture.runAsync(() -> {
                try {
                    String testParams = "{\"test\": \"stress_test_" + requestId + "\"}";
                    String response = jingyuSendService.request(JingyuURI.SEND_MSG_TO_GROUP, testParams);
                    logger.debug("压力测试请求 {} 响应: {}", requestId, response);
                } catch (Exception e) {
                    logger.error("压力测试请求 {} 异常", requestId, e);
                }
            }, executor);
        }
        
        // 等待所有请求完成
        try {
            executor.shutdown();
            executor.awaitTermination(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 打印最终状态
        Map<String, Object> finalStatus = monitorService.getAllStatus();
        logger.info("压力测试后状态: {}", finalStatus);
        
        logger.info("高并发压力测试完成");
    }
}
