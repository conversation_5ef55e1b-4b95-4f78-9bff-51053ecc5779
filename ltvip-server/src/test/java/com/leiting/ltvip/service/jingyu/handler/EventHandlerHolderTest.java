package com.leiting.ltvip.service.jingyu.handler;


import com.leiting.ltvip.test.AppTest;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2025/5/19 17:35
 */
public class EventHandlerHolderTest extends AppTest {
    
    @Test
    public void testSendGroup() {
        EventHandlerHolder.handle(groupMessageAtMore());
    }
    
    @Test
    public void testDontSendUser() {
        EventHandlerHolder.handle(userMessageDontSend());
    }
    
    @Test
    public void testNeedSendUser() {
        EventHandlerHolder.handle(userMessageNeedSend());
    }
    
    public String groupMessageNormal() {
        return "{\"app_info\":\"4736304586615564871\",\"at_list\":\"\",\"channel_msg_sn\":\"10933475\",\"desc\":\"\",\"event_type\":40024,\"file_id\":\"\"," +
                       "\"hint\":\"FjBSmLQoVBhBxdb1J1QA.ajSXAK.1747895657\",\"href\":\"\",\"md5\":\"\",\"msg_content\":\"角色有多少个门派\",\"msg_id\":\"10933475\"," +
                       "\"msg_time\":\"2025-05-22 14:34:15.000\",\"msg_type\":1,\"quote_app_info\":\"\",\"receiver_id\":\"group2qsweNwMe0hZ9lFH0JEI\"," +
                       "\"robot_id\":\"accARlvqjlNbt6Izt45o8ia\",\"sender_external_user_id\":\"wmqtUWCQAAy8fwWWRzvomRd_LGX1hGbg\"," +
                       "\"sender_id\":\"acc0K4q99WQo9ITHPbickv6\",\"sender_type\":2,\"sender_union_id\":\"oU9NN6TcK_hoA_gIpM5C0FUF7H98\",\"title\":\"\"," +
                       "\"voice_time\":\"0\"}";
    }
    
    public String groupMessageAt() {
        return "{\"app_info\":\"3534103835981097077\",\"at_list\":\"accARlvqjlNbt6Izt45o8ia\",\"channel_msg_sn\":\"10934533\",\"desc\":\"\"," +
                       "\"event_type\":40024,\"file_id\":\"\",\"hint\":\"H09NxgREnBhBzFWXwCoA.cwuhxw.1747902799\",\"href\":\"\",\"md5\":\"\"," +
                       "\"msg_content\":\"@问道小师姐 角色如何转门派\",\"msg_id\":\"10934533\",\"msg_time\":\"2025-05-22 16:33:16.000\",\"msg_type\":1," +
                       "\"quote_app_info\":\"\",\"receiver_id\":\"group2qsweNwMe0hZ9lFH0JEI\",\"robot_id\":\"accARlvqjlNbt6Izt45o8ia\"," +
                       "\"sender_external_user_id\":\"wmqtUWCQAAy8fwWWRzvomRd_LGX1hGbg\",\"sender_id\":\"acc0K4q99WQo9ITHPbickv6\",\"sender_type\":2," +
                       "\"sender_union_id\":\"oU9NN6TcK_hoA_gIpM5C0FUF7H98\",\"title\":\"\",\"voice_time\":\"0\"}";
    }
    
    public String groupMessageAtMore() {
        return "{\"app_info\":\"6495132345327550177\",\"at_list\":\"accUL1amo9ji5ttD3c5LEyt;accARlvqjlNbt6Izt45o8ia\",\"channel_msg_sn\":\"3551474\"," +
                       "\"desc\":\"\",\"event_type\":40024,\"file_id\":\"\",\"hint\":\"ZZanZBwoVBhB0JntwisA.TIRGao.1747907490\",\"href\":\"\",\"md5\":\"\"," +
                       "\"msg_content\":\"@问道小师姐 @问道小师姐 测试艾特多人\",\"msg_id\":\"3551474\",\"msg_time\":\"2025-05-22 17:51:27.000\",\"msg_type\":1," +
                       "\"quote_app_info\":\"\",\"receiver_id\":\"group2qsweNwMe0hZ9lFH0JEI\",\"robot_id\":\"accUL1amo9ji5ttD3c5LEyt\"," +
                       "\"sender_external_user_id\":\"wmqtUWCQAAy8fwWWRzvomRd_LGX1hGbg\",\"sender_id\":\"acc0K4q99WQo9ITHPbickv6\",\"sender_type\":2," +
                       "\"sender_union_id\":\"oU9NN6TcK_hoA_gIpM5C0FUF7H98\",\"title\":\"\",\"voice_time\":\"0\"}";
    }
    
    public String userMessageDontSend() {
        return "{\"account_type\":2,\"app_info\":\"RaHWtIeUSxOUB2K\",\"channel_msg_sn\":\"3574135\",\"desc\":\"\",\"event_type\":40023,\"file_id\":\"\"," +
                       "\"hint\":\"ZD2ArotixxhCAxB2eEsA.njUdOT.**********\",\"href\":\"\",\"md5\":\"\",\"msg_content\":\"我是王饱饱\",\"msg_id\":\"3574135\"," +
                       "\"msg_time\":\"2025-05-23 09:31:07.000\",\"msg_type\":1,\"pv_tag\":0,\"quote_app_info\":\"\"," +
                       "\"receiver_id\":\"accUL1amo9ji5ttD3c5LEyt\",\"robot_id\":\"accUL1amo9ji5ttD3c5LEyt\"," +
                       "\"sender_external_user_id\":\"wmqtUWCQAAy8fwWWRzvomRd_LGX1hGbg\",\"sender_id\":\"acc0K4q99WQo9ITHPbickv6\",\"sender_type\":2," +
                       "\"sender_union_id\":\"oU9NN6TcK_hoA_gIpM5C0FUF7H98\",\"title\":\"\",\"voice_time\":\"0\"}";
    }
    
    public String userMessageNeedSend() {
        return "{\"account_type\":2,\"app_info\":\"RaHWtIeUSxOUB2K\",\"channel_msg_sn\":\"3574135\",\"desc\":\"\",\"event_type\":40023,\"file_id\":\"\"," +
                       "\"hint\":\"ZD2ArotixxhCAxB2eEsA.njUdOT.**********\",\"href\":\"\",\"md5\":\"\",\"msg_content\":\"#AI求助# 我是王饱饱\"," +
                       "\"msg_id\":\"3574135\"," +
                       "\"msg_time\":\"2025-05-23 09:31:07.000\",\"msg_type\":1,\"pv_tag\":0,\"quote_app_info\":\"\"," +
                       "\"receiver_id\":\"accUL1amo9ji5ttD3c5LEyt\",\"robot_id\":\"accUL1amo9ji5ttD3c5LEyt\"," +
                       "\"sender_external_user_id\":\"wmqtUWCQAAy8fwWWRzvomRd_LGX1hGbg\",\"sender_id\":\"acc0K4q99WQo9ITHPbickv6\",\"sender_type\":2," +
                       "\"sender_union_id\":\"oU9NN6TcK_hoA_gIpM5C0FUF7H98\",\"title\":\"\",\"voice_time\":\"0\"}";
    }
    
}
