package com.leiting.ltvip;

import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 简单测试类
 * 验证基本功能，不依赖Spring上下文
 * <AUTHOR>
 * @date 2025/1/27
 */
public class SimpleTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleTest.class);
    
    @Test
    public void testBasicFunctionality() {
        logger.info("开始基本功能测试");
        
        // 测试常量类
        testConstants();
        
        // 测试响应类
        testResponse();
        
        logger.info("基本功能测试完成");
    }
    
    private void testConstants() {
        logger.info("测试常量类");
        
        try {
            // 测试JingyuResilienceConst
            Class.forName("com.leiting.ltvip.constant.JingyuResilienceConst");
            logger.info("JingyuResilienceConst 加载成功");
            
            // 测试JingyuURI
            Class.forName("com.leiting.ltvip.constant.JingyuURI");
            logger.info("JingyuURI 加载成功");
            
        } catch (ClassNotFoundException e) {
            logger.error("常量类加载失败", e);
        }
    }
    
    private void testResponse() {
        logger.info("测试响应类");
        
        try {
            // 测试JingyuResponse
            Class.forName("com.leiting.ltvip.model.response.JingyuResponse");
            logger.info("JingyuResponse 加载成功");
            
            // 创建响应对象测试
            Object successResponse = createSuccessResponse();
            Object failResponse = createFailResponse();
            
            logger.info("响应对象创建成功: success={}, fail={}", 
                successResponse != null, failResponse != null);
            
        } catch (Exception e) {
            logger.error("响应类测试失败", e);
        }
    }
    
    private Object createSuccessResponse() {
        try {
            Class<?> responseClass = Class.forName("com.leiting.ltvip.model.response.JingyuResponse");
            java.lang.reflect.Method successMethod = responseClass.getMethod("success", Object.class);
            return successMethod.invoke(null, "test data");
        } catch (Exception e) {
            logger.error("创建成功响应失败", e);
            return null;
        }
    }
    
    private Object createFailResponse() {
        try {
            Class<?> responseClass = Class.forName("com.leiting.ltvip.model.response.JingyuResponse");
            java.lang.reflect.Method failMethod = responseClass.getMethod("fail", int.class, String.class);
            return failMethod.invoke(null, 500, "test error");
        } catch (Exception e) {
            logger.error("创建失败响应失败", e);
            return null;
        }
    }
    
    @Test
    public void testAnnotations() {
        logger.info("测试注解类");
        
        try {
            // 测试JingyuResilience注解
            Class.forName("com.leiting.ltvip.annotation.JingyuResilience");
            logger.info("JingyuResilience 注解加载成功");
            
        } catch (ClassNotFoundException e) {
            logger.error("注解类加载失败", e);
        }
    }
    
    @Test
    public void testUtilities() {
        logger.info("测试工具类");
        
        try {
            // 测试MethodParamUtil
            Class.forName("com.leiting.ltvip.util.MethodParamUtil");
            logger.info("MethodParamUtil 工具类加载成功");
            
            // 测试参数查找功能
            testParameterSearch();
            
        } catch (ClassNotFoundException e) {
            logger.error("工具类加载失败", e);
        }
    }
    
    private void testParameterSearch() {
        try {
            Class<?> utilClass = Class.forName("com.leiting.ltvip.util.MethodParamUtil");
            java.lang.reflect.Method findMethod = utilClass.getMethod("findParamByType", Object[].class, Class.class);
            
            // 测试参数查找
            Object[] args = {"test", 123, true};
            Object result = findMethod.invoke(null, args, String.class);
            
            logger.info("参数查找测试结果: {}", result);
            
        } catch (Exception e) {
            logger.error("参数查找测试失败", e);
        }
    }
}
